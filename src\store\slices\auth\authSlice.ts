import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { axiosPrivate } from '../../../lib/axios';
import { AxiosError } from 'axios';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status: string;
  profileImage: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean; // Added this property
  loading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false, // Added this property
  loading: false,
  error: null,
};

const fetchUserWithRefresh = async (): Promise<{ user: User }> => {
  try {
    const response = await axiosPrivate.get('/auth/me');
    return response.data;
  } catch (error: unknown) {
    const axiosError = error as AxiosError<{ message: string }>;

    if (axiosError.response?.status === 401) {
      const errorMessage = axiosError.response?.data?.message || '';

      if (errorMessage.includes('Token has expired') || errorMessage.includes('Invalid token')) {
        try {
          await axiosPrivate.post('/auth/refresh-token');

          const retryResponse = await axiosPrivate.get('/auth/me');
          return retryResponse.data;
        } catch (refreshError: unknown) {
          const refreshAxiosError = refreshError as AxiosError<{ message: string }>;
          console.log('Token refresh failed:', refreshError);

          // If refresh token is also expired/invalid, throw a specific error
          if (refreshAxiosError.response?.status === 401) {
            const refreshErrorMessage = refreshAxiosError.response?.data?.message || '';
            if (refreshErrorMessage.includes('expired') || refreshErrorMessage.includes('invalid')) {
              throw new Error('REFRESH_TOKEN_EXPIRED');
            }
          }

          throw error;
        }
      }
    }

    throw error;
  }
};

// Async thunk for sign-in
export const signIn = createAsyncThunk(
  'auth/signIn',
  async (
    credentials: { email: string; password: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await axiosPrivate.post('/auth/signin', credentials);
      return response.data;
    } catch (err: unknown) {
      const axiosError = err as AxiosError<{ message: string }>;
      return rejectWithValue(
        axiosError.response?.data?.message || 'Sign-in failed'
      );
    }
  }
);

export const checkAuthStatus = createAsyncThunk(
  'auth/checkStatus',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      const data = await fetchUserWithRefresh();
      console.log('Authentication check successful:', data.user?.email);
      return data;
    } catch (err: unknown) {
      // Handle refresh token expiration
      if (err instanceof Error && err.message === 'REFRESH_TOKEN_EXPIRED') {
        console.log('Refresh token expired - logging out user');
        // Dispatch logout to clear all client-side state
        dispatch(signOut());
        return rejectWithValue('Your session has expired. Please log in again.');
      }

      const axiosError = err as AxiosError<{ message: string }>;
      const errorMessage = axiosError.response?.data?.message || 'Authentication check failed';
      console.log('Authentication check failed:', errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axiosPrivate.post('/auth/refresh-token');
      console.log('Token refresh successful');
      return response.data;
    } catch (err: unknown) {
      const axiosError = err as AxiosError<{ message: string }>;
      const errorMessage = axiosError.response?.data?.message || 'Token refresh failed';
      console.log('Token refresh failed:', errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);


export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axiosPrivate.post('/auth/signout');
      console.log('Logout successful');

      return response.data;
    } catch (err: unknown) {
      const axiosError = err as AxiosError<{ message: string }>;
      const errorMessage = axiosError.response?.data?.message || 'Logout failed';
      console.log('Logout failed:', errorMessage);

      return rejectWithValue(errorMessage);
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    signOut: (state) => {
      state.user = null;
      state.isAuthenticated = false; // Reset authentication status
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Sign-in cases
      .addCase(signIn.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(signIn.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.user = action.payload.user;
        state.isAuthenticated = true; // Set to true on successful sign-in
      })
      .addCase(signIn.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.user = null;
        state.isAuthenticated = false; // Set to false on failed sign-in
      })
      // Check auth status cases
      .addCase(checkAuthStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(checkAuthStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.isAuthenticated = true;
      })
      .addCase(checkAuthStatus.rejected, (state, action) => {
        state.loading = false;
        state.user = null;
        state.isAuthenticated = false;
        state.error = action.payload as string;
      })
      .addCase(refreshToken.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(refreshToken.fulfilled, (state) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(refreshToken.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.user = null;
        state.isAuthenticated = false;
      })
      .addCase(logout.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(logout.fulfilled, (state) => {
        state.loading = false;
        state.error = null;
        state.user = null;
        state.isAuthenticated = false;
      })
      .addCase(logout.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.user = null;
        state.isAuthenticated = false;
      });
  },
});

export const { signOut, clearError } = authSlice.actions;
export default authSlice.reducer;
