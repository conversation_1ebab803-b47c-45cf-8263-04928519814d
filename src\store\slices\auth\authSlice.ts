import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { axiosPrivate } from '../../../lib/axios';
import { AxiosError } from 'axios';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status: string;
  profileImage: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean; // Added this property
  loading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false, // Added this property
  loading: false,
  error: null,
};

// Helper function to handle auth/me with automatic token refresh
const fetchUserWithRefresh = async (): Promise<{ user: User }> => {
  try {
    // First try to get user info
    const response = await axiosPrivate.get('/auth/me');
    return response.data;
  } catch (error: unknown) {
    const axiosError = error as AxiosError;

    // If token is expired (401), try to refresh
    if (axiosError.response?.status === 401) {
      try {
        // Refresh the token
        await axiosPrivate.post('/auth/refresh-token');

        // Retry getting user info with new token
        const retryResponse = await axiosPrivate.get('/auth/me');
        return retryResponse.data;
      } catch {
        // If refresh fails, throw the original error
        throw error;
      }
    }

    // If it's not a 401 error, throw the original error
    throw error;
  }
};

// Async thunk for sign-in
export const signIn = createAsyncThunk(
  'auth/signIn',
  async (
    credentials: { email: string; password: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await axiosPrivate.post('/auth/signin', credentials);
      return response.data;
    } catch (err: unknown) {
      const axiosError = err as AxiosError<{ message: string }>;
      return rejectWithValue(
        axiosError.response?.data?.message || 'Sign-in failed'
      );
    }
  }
);

// Check if user is authenticated with automatic token refresh
export const checkAuthStatus = createAsyncThunk(
  'auth/checkStatus',
  async (_, { rejectWithValue }) => {
    try {
      const data = await fetchUserWithRefresh();
      return data;
    } catch (err: unknown) {
      const axiosError = err as AxiosError<{ message: string }>;
      return rejectWithValue(
        axiosError.response?.data?.message || 'Authentication check failed'
      );
    }
  }
);

// Async thunk for refresh token
export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axiosPrivate.post('/auth/refresh-token');
      return response.data;
    } catch (err: unknown) {
      const axiosError = err as AxiosError<{ message: string }>;
      return rejectWithValue(
        axiosError.response?.data?.message || 'Token refresh failed'
      );
    }
  }
);

// Async thunk for logout
export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axiosPrivate.post('/auth/signout');
      return response.data;
    } catch (err: unknown) {
      const axiosError = err as AxiosError<{ message: string }>;
      return rejectWithValue(
        axiosError.response?.data?.message || 'Logout failed'
      );
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    signOut: (state) => {
      state.user = null;
      state.isAuthenticated = false; // Reset authentication status
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Sign-in cases
      .addCase(signIn.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(signIn.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.user = action.payload.user;
        state.isAuthenticated = true; // Set to true on successful sign-in
      })
      .addCase(signIn.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.user = null;
        state.isAuthenticated = false; // Set to false on failed sign-in
      })
      // Check auth status cases
      .addCase(checkAuthStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(checkAuthStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.isAuthenticated = true;
      })
      .addCase(checkAuthStatus.rejected, (state, action) => {
        state.loading = false;
        state.user = null;
        state.isAuthenticated = false;
        state.error = action.payload as string;
      })
      // Refresh token cases
      .addCase(refreshToken.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(refreshToken.fulfilled, (state) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(refreshToken.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.user = null;
        state.isAuthenticated = false;
      })
      .addCase(logout.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(logout.fulfilled, (state) => {
        state.loading = false;
        state.error = null;
        state.user = null;
        state.isAuthenticated = false;
      })
      .addCase(logout.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.user = null;
        state.isAuthenticated = false;
      });
  },
});

export const { signOut, clearError } = authSlice.actions;
export default authSlice.reducer;
