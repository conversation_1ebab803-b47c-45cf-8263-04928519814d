import Image from 'next/image';
import React from 'react';

interface Says {
  say: string;
  name: string;
  job: string;
}

function ClientSays({ say, name, job }: Says) {
  return (
    <div className="hover:border-primary-300 dark:hover:border-primary-300 items flex h-[284px] w-[350px] cursor-pointer flex-col overflow-hidden rounded-lg border-1 p-4 text-start shadow-lg dark:border-gray-700">
      <div className="icon">
        <Image
          src="/assets/svgs/say.svg"
          width={50}
          height={50}
          alt="Says Logo"
        />
      </div>
      <p className="line-clamp-3 py-8 text-neutral-600">{say}</p>
      <h1 className="pl-2 font-bold text-black dark:text-amber-50">{name}</h1>
      <h4 className="text-neutral-600">{job}</h4>
    </div>
  );
}

export default ClientSays;
