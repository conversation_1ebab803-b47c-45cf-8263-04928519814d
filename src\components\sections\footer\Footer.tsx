'use client';
import React from 'react';
import { Linkedin, Instagram } from 'lucide-react';
import { useTheme } from 'next-themes';
import Image from 'next/image';

const Footer = () => {
  const { theme } = useTheme();

  const [mounted, setMounted] = React.useState(false);
  React.useEffect(() => setMounted(true), []);
  // console.log(theme)
  return (
    <footer className="bg-white px-4 py-12 sm:px-6 lg:px-8 dark:bg-black dark:text-white">
      <div className="mx-auto max-w-7xl">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {/* Logo and Description */}
          <div className="lg:col-span-1">
            <div className="mb-4 items-center">
              {/* SVG M Logo */}
              {mounted && theme == 'light' ? (
                <Image
                  src="/assets/landing-page/blackLogo.svg"
                  alt="logo"
                  width={50}
                  height={40}
                />
              ) : (
                <Image
                  src="/assets/landing-page/logo.svg"
                  alt="logo"
                  width={50}
                  height={40}
                />
              )}
              <h3 className="pt-2 text-xl font-bold text-gray-900 dark:text-white">
                Coach Mahmoud
              </h3>
            </div>
            <p className="text-sm leading-relaxed text-gray-600 dark:text-gray-300">
              Empowering individuals to unlock their full potential and create
              extraordinary results.
            </p>
          </div>

          {/* Quick Links */}
          <div className="lg:col-span-1">
            <h4 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
              Quick Links
            </h4>
            <ul className="space-y-3">
              <li>
                <a
                  href="#about"
                  className="text-gray-600 transition-colors duration-200 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  About
                </a>
              </li>
              <li>
                <a
                  href="#events"
                  className="text-gray-600 transition-colors duration-200 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  Events
                </a>
              </li>
              <li>
                <a
                  href="#store"
                  className="text-gray-600 transition-colors duration-200 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  Store
                </a>
              </li>
              <li>
                <a
                  href="#contact"
                  className="text-gray-600 transition-colors duration-200 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  Contact
                </a>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div className="lg:col-span-1">
            <h4 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
              Services
            </h4>
            <ul className="space-y-3">
              <li>
                <a
                  href="#coaching"
                  className="text-gray-600 transition-colors duration-200 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  1-on-1 Coaching
                </a>
              </li>
              <li>
                <a
                  href="#workshops"
                  className="text-gray-600 transition-colors duration-200 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  Group Workshops
                </a>
              </li>
              <li>
                <a
                  href="#courses"
                  className="text-gray-600 transition-colors duration-200 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  Online Courses
                </a>
              </li>
              <li>
                <a
                  href="#speaking"
                  className="text-gray-600 transition-colors duration-200 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  Speaking Events
                </a>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div className="lg:col-span-1">
            <h4 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
              Contact
            </h4>
            <div className="space-y-3">
              <div>
                <a
                  href="mailto:<EMAIL>"
                  className="block text-gray-600 transition-colors duration-200 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  <EMAIL>
                </a>
              </div>
              <div>
                <a
                  href="tel:+15551234567"
                  className="block text-gray-600 transition-colors duration-200 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  +****************
                </a>
              </div>

              {/* Social Media Icons */}
              <div className="flex space-x-3 pt-2">
                <a
                  href="#linkedin"
                  className="text-gray-600 transition-colors duration-200 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                  aria-label="LinkedIn"
                >
                  <Linkedin size={20} />
                </a>
                <a
                  href="#instagram"
                  className="text-gray-600 transition-colors duration-200 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                  aria-label="Instagram"
                >
                  <Instagram size={20} />
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Border */}
        <div className="mt-8 border-t border-gray-200 pt-8 dark:border-gray-700">
          <div className="flex flex-col items-center justify-between sm:flex-row">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              © {new Date().getFullYear()} Coach Mahmoud. All rights reserved.
            </p>
            <div className="mt-4 flex space-x-6 sm:mt-0">
              <a
                href="#privacy"
                className="text-sm text-gray-500 transition-colors duration-200 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
              >
                Privacy Policy
              </a>
              <a
                href="#terms"
                className="text-sm text-gray-500 transition-colors duration-200 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
              >
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
