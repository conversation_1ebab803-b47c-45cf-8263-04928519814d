'use client';

import SectionHeading from '@/components/molecules/SectionHeading';
// import { Swiper, SwiperSlide } from 'swiper/react';
// import { Navigation, EffectCoverflow, Autoplay } from 'swiper/modules';
// import 'swiper/css';
// import 'swiper/css/navigation';
// import 'swiper/css/effect-coverflow';
// import Image from 'next/image';

// const images = [
//   { src: '/assets/about/carousel-1.webp', width: 400, height: 200 },
//   { src: '/assets/about/carousel-2.webp', width: 400, height: 200 },
//   { src: '/assets/about/carousel-3.webp', width: 400, height: 200 },
//   { src: '/assets/about/carousel-4.webp', width: 400, height: 200 },
//   { src: '/assets/about/carousel-5.webp', width: 400, height: 200 },
// ];

// const ImageCarousel = () => {
//   return (
//     <div style={{ maxWidth: '100%', overflow: 'hidden', padding: '0 20px' }}>
//       <Swiper
//         modules={[Navigation, EffectCoverflow, Autoplay]}
//         effect={'coverflow'}
//         grabCursor={true}
//         centeredSlides={true}
//         slidesPerView={3} // Ensures 3 slides are visible at once
//         spaceBetween={5} // Further reduced gap to 5px
//         loop={true} // Infinite looping
//         coverflowEffect={{
//           rotate: 0,
//           stretch: 0,
//           depth: 200, // Increased depth for a more pronounced 3D effect
//           modifier: 1, // Adjusted for smoother transitions
//           slideShadows: false, // Disabled shadows for simplicity
//         }}
//         navigation
//         autoplay={{ delay: 3000, disableOnInteraction: false }}
//         style={{ width: '100%', maxWidth: '1200px', margin: '0 auto' }}
//       >
//         {images.map((image, index) => (
//           <SwiperSlide
//             key={index}
//             style={{
//               display: 'flex',
//               justifyContent: 'center',
//               alignItems: 'center',
//               width: 'auto', // Let Swiper handle width dynamically
//               maxWidth: '400px', // Cap slide width to match image
//             }}
//           >
//             <Image
//               src={image.src}
//               alt={`Carousel Image ${index + 1}`}
//               width={image.width}
//               height={image.height}
//               style={{
//                 width: '100%',
//                 height: 'auto',
//                 objectFit: 'cover', // Changed to 'cover' for better fit
//               }}
//             />
//           </SwiperSlide>
//         ))}
//       </Swiper>
//     </div>
//   );
// };

// export default ImageCarousel;
// import Image from 'next/image';
// import React, { useEffect, useState } from 'react';

// const images = [
//   '/assets/about/carousel-1.webp',
//   '/assets/about/carousel-2.webp',
//   '/assets/about/carousel-3.webp',
//   '/assets/about/carousel-4.webp',
//   '/assets/about/carousel-5.webp',
// ];

// const ImageCarousel = () => {
//   const [currentIndex, setCurrentIndex] = useState(0);
//   const [isAnimating, setIsAnimating] = useState(false);

//   const updateCarousel = (index: number) => {
//     if (isAnimating) return;
//     setIsAnimating(true);
//     setCurrentIndex((index + images.length) % images.length);
//     setTimeout(() => setIsAnimating(false), 800);
//   };

//   const getCardClass = (index: number) => {
//     const diff = (index - currentIndex + images.length) % images.length;

//     // Center image - largest and most prominent
//     if (diff === 0)
//       return 'z-20 scale-110 w-[220px] sm:w-[280px] md:w-[340px] lg:w-[400px]';

//     // Right side images
//     if (diff === 1)
//       return 'z-10 translate-x-[180px] sm:translate-x-[230px] md:translate-x-[280px] lg:translate-x-[330px] scale-75 opacity-80';
//     if (diff === 2)
//       return 'z-5 translate-x-[320px] sm:translate-x-[390px] md:translate-x-[460px] lg:translate-x-[530px] scale-60 opacity-60';

//     // Left side images (mirror of right side)
//     if (diff === images.length - 1)
//       return 'z-10 -translate-x-[180px] sm:-translate-x-[230px] md:-translate-x-[280px] lg:-translate-x-[330px] scale-75 opacity-80';
//     if (diff === images.length - 2)
//       return 'z-5 -translate-x-[320px] sm:-translate-x-[390px] md:-translate-x-[460px] lg:-translate-x-[530px] scale-60 opacity-60';

//     return 'opacity-0 pointer-events-none scale-50';
//   };

//   const handleKeyDown = (e: KeyboardEvent) => {
//     if (e.key === 'ArrowLeft') updateCarousel(currentIndex - 1);
//     if (e.key === 'ArrowRight') updateCarousel(currentIndex + 1);
//   };

//   useEffect(() => {
//     window.addEventListener('keydown', handleKeyDown);
//     return () => window.removeEventListener('keydown', handleKeyDown);
//   });

//   return (
//     <div className="mx-auto mt-10 w-full max-w-[1100px] px-4">
//       <SectionHeading
//         headline="A Journey of Real Impact"
//         subLine="Moments that shaped our mission — captured from real coaching sessions, workshops, and community growth.
// "
//       />
//       <div className="relative flex h-[280px] items-center justify-center overflow-hidden sm:h-[360px] md:h-[420px] lg:h-[480px]">
//         <button
//           onClick={() => updateCarousel(currentIndex - 1)}
//           className="absolute top-1/2 left-4 z-20 flex h-10 w-10 -translate-y-1/2 transform cursor-pointer items-center justify-center rounded-full bg-black/20 backdrop-blur-sm transition-all duration-300 hover:bg-black/40"
//           aria-label="Previous"
//         >
//           <svg
//             width="20"
//             height="20"
//             viewBox="0 0 24 24"
//             fill="none"
//             xmlns="http://www.w3.org/2000/svg"
//           >
//             <path
//               d="M15 6L9 12L15 18"
//               stroke="white"
//               strokeWidth="2"
//               strokeLinecap="round"
//               strokeLinejoin="round"
//             />
//           </svg>
//         </button>

//         <div className="relative flex h-full w-full items-center justify-center">
//           {images.map((src, index) => {
//             return (
//               <div
//                 key={index}
//                 className={`absolute h-[240px] w-[180px] cursor-pointer overflow-hidden rounded-2xl transition-all duration-700 ease-out sm:h-[280px] sm:w-[220px] md:h-[320px] md:w-[260px] lg:h-[360px] lg:w-[300px] ${getCardClass(index)}`}
//                 onClick={() => updateCarousel(index)}
//               >
//                 <Image
//                   width={400}
//                   height={360}
//                   src={src}
//                   alt={`Slide ${index}`}
//                   className="h-full w-full rounded-2xl object-cover"
//                 />
//               </div>
//             );
//           })}
//         </div>

//         <button
//           onClick={() => updateCarousel(currentIndex + 1)}
//           className="absolute top-1/2 right-4 z-20 flex h-10 w-10 -translate-y-1/2 transform cursor-pointer items-center justify-center rounded-full bg-black/20 backdrop-blur-sm transition-all duration-300 hover:bg-black/40"
//           aria-label="Next"
//         >
//           <svg
//             width="20"
//             height="20"
//             viewBox="0 0 24 24"
//             fill="none"
//             xmlns="http://www.w3.org/2000/svg"
//           >
//             <path
//               d="M9 6L15 12L9 18"
//               stroke="white"
//               strokeWidth="2"
//               strokeLinecap="round"
//               strokeLinejoin="round"
//             />
//           </svg>
//         </button>
//       </div>
//       {/* Dots below carousel */}
//       <div className="mt-8 hidden justify-center gap-2 md:flex">
//         {images.map((_, i) => (
//           <div
//             key={i}
//             onClick={() => updateCarousel(i)}
//             className={`h-3 w-3 cursor-pointer rounded-full transition-all duration-300 ${
//               i === currentIndex ? 'bg-primary scale-110' : 'bg-main'
//             }`}
//           ></div>
//         ))}
//       </div>
//     </div>
//   );
// };

// export default ImageCarousel;
import Image from 'next/image';
import React, { useEffect, useState, useCallback } from 'react';

const images = [
  '/assets/about/carousel-1.webp',
  '/assets/about/carousel-2.webp',
  '/assets/about/carousel-3.webp',
  '/assets/about/carousel-4.webp',
  '/assets/about/carousel-5.webp',
];

// إزالة SectionHeading المكرر - استخدم الموجود بالفعل في مشروعك

const ImageCarousel: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [isAnimating, setIsAnimating] = useState<boolean>(false);

  const updateCarousel = useCallback(
    (newIndex: number): void => {
      if (isAnimating) return;

      setIsAnimating(true);
      const normalizedIndex =
        ((newIndex % images.length) + images.length) % images.length;
      setCurrentIndex(normalizedIndex);

      setTimeout(() => setIsAnimating(false), 400);
    },
    [isAnimating]
  );

  const getCardClass = (index: number): string => {
    const diff = (index - currentIndex + images.length) % images.length;

    // الصورة المركزية
    if (diff === 0) {
      return 'z-30 scale-100 opacity-100 transform translate-x-0';
    }

    // الصور على اليمين
    if (diff === 1) {
      return 'z-20 scale-75 opacity-60 transform translate-x-[120px] sm:translate-x-[150px] md:translate-x-[180px] lg:translate-x-[220px]';
    }
    if (diff === 2) {
      return 'z-10 scale-60 opacity-40 transform translate-x-[200px] sm:translate-x-[250px] md:translate-x-[300px] lg:translate-x-[360px]';
    }

    // الصور على اليسار
    if (diff === images.length - 1) {
      return 'z-20 scale-75 opacity-60 transform -translate-x-[120px] sm:-translate-x-[150px] md:-translate-x-[180px] lg:-translate-x-[220px]';
    }
    if (diff === images.length - 2) {
      return 'z-10 scale-60 opacity-40 transform -translate-x-[200px] sm:-translate-x-[250px] md:-translate-x-[300px] lg:-translate-x-[360px]';
    }

    // الصور المخفية
    return 'z-0 scale-50 opacity-0 pointer-events-none';
  };

  const handleKeyDown = useCallback(
    (e: KeyboardEvent): void => {
      if (e.key === 'ArrowLeft') {
        e.preventDefault();
        updateCarousel(currentIndex - 1);
      }
      if (e.key === 'ArrowRight') {
        e.preventDefault();
        updateCarousel(currentIndex + 1);
      }
    },
    [currentIndex, updateCarousel]
  );

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  const handleImageClick = (index: number): void => {
    updateCarousel(index);
  };

  const handleButtonClick = (direction: 'prev' | 'next'): void => {
    if (direction === 'prev') {
      updateCarousel(currentIndex - 1);
    } else {
      updateCarousel(currentIndex + 1);
    }
  };

  return (
    <div className="mx-auto mt-10 w-full max-w-[1100px] px-4">
      <SectionHeading
        headline="A Journey of Real Impact"
        subLine="Moments that shaped our mission — captured from real coaching sessions, workshops, and community growth."
      />

      <div className="relative flex h-[300px] items-center justify-center overflow-hidden sm:h-[380px] md:h-[420px] lg:h-[480px]">
        {/* زر السابق */}
        <button
          onClick={() => handleButtonClick('prev')}
          disabled={isAnimating}
          className="absolute top-1/2 left-4 z-40 flex h-12 w-12 -translate-y-1/2 transform cursor-pointer items-center justify-center rounded-full bg-white shadow-xl transition-all duration-300 hover:scale-110 hover:shadow-2xl disabled:cursor-not-allowed disabled:opacity-50"
          aria-label="Previous image"
          type="button"
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M15 6L9 12L15 18"
              stroke="#4F46E5"
              strokeWidth="2.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>

        {/* الصور */}
        <div className="relative flex h-full w-full items-center justify-center">
          {images.map((src, index) => (
            <div
              key={index}
              className={`absolute h-[240px] w-[180px] cursor-pointer overflow-hidden rounded-2xl shadow-xl transition-all duration-500 ease-out sm:h-[300px] sm:w-[230px] md:h-[340px] md:w-[260px] lg:h-[380px] lg:w-[300px] ${getCardClass(index)}`}
              onClick={() => handleImageClick(index)}
              style={{
                willChange: 'transform, opacity',
              }}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleImageClick(index);
                }
              }}
            >
              <Image
                src={src}
                alt={`Carousel slide ${index + 1}`}
                width={300}
                height={380}
                className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
                priority={index <= 2}
                placeholder="blur"
                blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
              />

              {/* تأثير خفيف للصورة المركزية */}
              {(index - currentIndex + images.length) % images.length === 0 && (
                <div className="pointer-events-none absolute inset-0 rounded-2xl ring-4 ring-indigo-500/30"></div>
              )}
            </div>
          ))}
        </div>

        {/* زر التالي */}
        <button
          onClick={() => handleButtonClick('next')}
          disabled={isAnimating}
          className="absolute top-1/2 right-4 z-40 flex h-12 w-12 -translate-y-1/2 transform cursor-pointer items-center justify-center rounded-full bg-white shadow-xl transition-all duration-300 hover:scale-110 hover:shadow-2xl disabled:cursor-not-allowed disabled:opacity-50"
          aria-label="Next image"
          type="button"
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9 6L15 12L9 18"
              stroke="#4F46E5"
              strokeWidth="2.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      </div>

      {/* النقاط السفلية */}
      <div className="mt-8 flex justify-center gap-2">
        {images.map((_, i) => (
          <button
            key={i}
            onClick={() => updateCarousel(i)}
            disabled={isAnimating}
            className={`h-2 rounded-full transition-all duration-300 ${
              i === currentIndex
                ? 'w-8 bg-indigo-600'
                : 'w-2 bg-gray-300 hover:bg-gray-400'
            }`}
            aria-label={`Go to slide ${i + 1}`}
            type="button"
          />
        ))}
      </div>
    </div>
  );
};

export default ImageCarousel;
