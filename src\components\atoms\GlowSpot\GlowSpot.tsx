// components/GlowSpot.tsx
import React from 'react';

interface GlowSpotProps {
  className?: string;
  size?: number;
  blur?: number;
  fromColor?: string;
  toColor?: string;
}

const GlowSpot: React.FC<GlowSpotProps> = ({
  className = '',
  size = 475,
  blur = 150,
  fromColor = '#F14646',
  toColor = '#FF0000',
}) => {
  return (
    <div
      className={`pointer-events-none absolute z-0 ${className}`}
      style={{
        width: size,
        height: size,
        filter: `blur(${blur}px)`,
        background: `radial-gradient(circle, ${fromColor}, ${toColor})`,
        borderRadius: '50%',
      }}
    />
  );
};

export default GlowSpot;
