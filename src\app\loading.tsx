// This file is moved to (MainUI)/loading.tsx for proper route group coverage
'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';

const LoadingPage = () => {
  return (
    <div className="fixed inset-0 z-50 flex min-h-screen items-center justify-center bg-gradient-to-br from-neutral-100 via-white to-neutral-200 dark:from-neutral-900 dark:via-black dark:to-neutral-800">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(241,70,70,0.1),transparent_50%)]" />
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(241,70,70,0.05)_50%,transparent_75%)]" />
      </div>

      {/* Main Loading Content */}
      <motion.div
        className="relative z-10 flex flex-col items-center justify-center space-y-8 px-4"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6, ease: 'easeOut' }}
      >
        {/* Logo with Pulse Animation */}
        <motion.div
          className="relative"
          animate={{
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        >
          {/* Glow Effect */}
          <motion.div
            className="absolute inset-0 rounded-full bg-primary-300 opacity-20 blur-xl"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.2, 0.4, 0.2],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
          
          {/* Logo */}
          <div className="relative z-10 flex h-20 w-20 items-center justify-center rounded-full bg-white shadow-2xl dark:bg-neutral-800">
            <Image
              src="/assets/landing-page/blackLogo.svg"
              alt="Personal Coach Logo"
              width={40}
              height={40}
              className="dark:hidden"
            />
            <Image
              src="/assets/landing-page/logo.svg"
              alt="Personal Coach Logo"
              width={40}
              height={40}
              className="hidden dark:block"
            />
          </div>
        </motion.div>

        {/* Loading Spinner */}
        <motion.div
          className="relative h-16 w-16"
          animate={{ rotate: 360 }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: 'linear',
          }}
        >
          {/* Outer Ring */}
          <div className="absolute inset-0 rounded-full border-4 border-neutral-200 dark:border-neutral-700" />
          
          {/* Animated Ring */}
          <motion.div
            className="absolute inset-0 rounded-full border-4 border-transparent border-t-primary-300"
            animate={{ rotate: 360 }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: 'linear',
            }}
          />
          
          {/* Inner Glow */}
          <motion.div
            className="absolute inset-2 rounded-full bg-gradient-to-r from-primary-300 to-primary-400 opacity-20"
            animate={{
              scale: [0.8, 1, 0.8],
              opacity: [0.2, 0.4, 0.2],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
        </motion.div>

        {/* Loading Text */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
        >
          <motion.h2
            className="mb-2 text-2xl font-bold text-neutral-800 dark:text-neutral-200"
            animate={{
              opacity: [0.7, 1, 0.7],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          >
            Loading...
          </motion.h2>
          
          <motion.p
            className="text-sm text-neutral-600 dark:text-neutral-400"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.6 }}
          >
            Preparing your coaching experience
          </motion.p>
        </motion.div>

        {/* Progress Dots */}
        <motion.div
          className="flex space-x-2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.9, duration: 0.6 }}
        >
          {[0, 1, 2].map((index) => (
            <motion.div
              key={index}
              className="h-2 w-2 rounded-full bg-primary-300"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: index * 0.2,
                ease: 'easeInOut',
              }}
            />
          ))}
        </motion.div>
      </motion.div>

      {/* Floating Elements */}
      <motion.div
        className="absolute top-1/4 left-1/4 h-4 w-4 rounded-full bg-primary-300 opacity-20"
        animate={{
          y: [-20, 20, -20],
          x: [-10, 10, -10],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />
      
      <motion.div
        className="absolute bottom-1/4 right-1/4 h-6 w-6 rounded-full bg-primary-400 opacity-15"
        animate={{
          y: [20, -20, 20],
          x: [10, -10, 10],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />
    </div>
  );
};

export default LoadingPage;
