import React from 'react';
import type { Metadata } from 'next';
import Navbar from '@/components/blocks/navbar/Navbar';
import GlowSpot from '@/components/atoms/GlowSpot/GlowSpot';
import Image from 'next/image';

export const metadata: Metadata = {
  title: 'Sign In - Personal Coach',
  description: 'Generated by create next app',
};

const AuthLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <>
      <nav className="nav-menu fixed z-30 w-full">
        <Navbar />
      </nav>

      <div className="flex min-h-[100dvh] items-center justify-evenly">
        <div className="relative flex h-full items-center justify-center">
          <GlowSpot className="top-[0] left-[0] z-[-1]" size={150} />
          <>
            <Image
              src="/assets/landing-page/blackLogo.svg"
              alt="logo"
              width={350}
              height={320}
              className="dark:hidden"
            />
            <Image
              src="/assets/landing-page/logo.svg"
              alt="logo"
              width={350}
              height={320}
              className="hidden dark:block"
            />
          </>
        </div>

        <main className="relative ms-20 flex h-full w-md items-center justify-stretch md:col-span-2">
          <GlowSpot className="top-[0] left-[0] z-[-1]" size={250} />

          {children}
        </main>
      </div>
    </>
  );
};

export default AuthLayout;
