'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Search, Home, ArrowLeft, MapPin } from 'lucide-react';
import GeneralBTN from '@/components/blocks/button/GeneralBTN';
import Image from 'next/image';
import Link from 'next/link';

const NotFoundPage = () => {
  const animationVariants = {
    container: {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          staggerChildren: 0.2,
          delayChildren: 0.1,
        },
      },
    },
    item: {
      hidden: { opacity: 0, y: 30 },
      visible: {
        opacity: 1,
        y: 0,
        transition: { duration: 0.8, ease: 'easeOut' },
      },
    },
    float: {
      y: [-15, 15, -15],
      rotate: [-5, 5, -5],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
    bounce: {
      y: [0, -20, 0],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  };

  const popularPages = [
    { name: 'Home', href: '/', icon: Home },
    { name: 'About Us', href: '/about-us', icon: MapPin },
    { name: 'Events', href: '/events', icon: Search },
  ];

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-neutral-100 via-white to-neutral-200 dark:from-neutral-900 dark:via-black dark:to-neutral-800">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_30%,rgba(241,70,70,0.1),transparent_50%)]" />
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(241,70,70,0.05)_50%,transparent_75%)]" />
      </div>

      {/* Floating Elements */}
      <motion.div
        className="absolute top-16 right-20 h-6 w-6 rounded-full bg-primary-300 opacity-20"
        animate={animationVariants.float}
      />
      <motion.div
        className="absolute bottom-20 left-16 h-10 w-10 rounded-full bg-primary-400 opacity-15"
        animate={{
          ...animationVariants.float,
          transition: { ...animationVariants.float.transition, delay: 1.5 },
        }}
      />
      <motion.div
        className="absolute top-1/3 left-10 h-4 w-4 rounded-full bg-red-300 opacity-25"
        animate={animationVariants.bounce}
      />

      {/* Main Content */}
      <div className="relative z-10 flex min-h-screen items-center justify-center px-4 py-16">
        <motion.div
          className="w-full max-w-3xl text-center"
          variants={animationVariants.container}
          initial="hidden"
          animate="visible"
        >
          {/* Logo */}
          <motion.div
            className="mb-8 flex justify-center"
            variants={animationVariants.item}
          >
            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-white shadow-xl dark:bg-neutral-800">
              <Image
                src="/assets/landing-page/blackLogo.svg"
                alt="Personal Coach Logo"
                width={32}
                height={32}
                className="dark:hidden"
              />
              <Image
                src="/assets/landing-page/logo.svg"
                alt="Personal Coach Logo"
                width={32}
                height={32}
                className="hidden dark:block"
              />
            </div>
          </motion.div>

          {/* 404 Number */}
          <motion.div
            className="mb-8"
            variants={animationVariants.item}
          >
            <motion.h1
              className="text-8xl font-bold text-transparent bg-gradient-to-r from-primary-300 via-primary-400 to-primary-500 bg-clip-text md:text-9xl"
              animate={{
                backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
              style={{
                backgroundSize: '200% 200%',
              }}
            >
              404
            </motion.h1>
          </motion.div>

          {/* Error Message */}
          <motion.div
            className="mb-8 space-y-4"
            variants={animationVariants.item}
          >
            <h2 className="text-3xl font-bold text-neutral-800 dark:text-neutral-200 md:text-4xl">
              Page Not Found
            </h2>
            
            <p className="mx-auto max-w-lg text-lg text-neutral-600 dark:text-neutral-400">
              The page you're looking for seems to have wandered off. Let's get you back on track to your coaching journey.
            </p>
          </motion.div>

          {/* Search Suggestion */}
          <motion.div
            className="mb-8"
            variants={animationVariants.item}
          >
            <div className="mx-auto max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-neutral-400" />
                <input
                  type="text"
                  placeholder="Search for pages..."
                  className="w-full rounded-full border border-neutral-300 bg-white py-3 pl-10 pr-4 text-neutral-800 placeholder-neutral-400 shadow-lg focus:border-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-300/20 dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-200 dark:placeholder-neutral-500"
                />
              </div>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            className="mb-8 flex flex-col items-center justify-center gap-4 sm:flex-row"
            variants={animationVariants.item}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <GeneralBTN
                text="Go Home"
                size="200px"
                height="45px"
                bg="primary-300"
                textColor="white"
                href="/"
                className="flex items-center justify-center gap-2"
              />
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <GeneralBTN
                text="Go Back"
                size="200px"
                height="45px"
                bg="transparent"
                textColor="primary-300"
                border="1"
                onClick={() => window.history.back()}
                className="flex items-center justify-center gap-2 border-primary-300"
              />
            </motion.div>
          </motion.div>

          {/* Popular Pages */}
          <motion.div
            className="space-y-4"
            variants={animationVariants.item}
          >
            <h3 className="text-lg font-semibold text-neutral-700 dark:text-neutral-300">
              Popular Pages
            </h3>
            
            <div className="flex flex-wrap justify-center gap-4">
              {popularPages.map((page, index) => {
                const IconComponent = page.icon;
                return (
                  <motion.div
                    key={page.name}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 + index * 0.1 }}
                  >
                    <Link
                      href={page.href}
                      className="flex items-center gap-2 rounded-full bg-white px-6 py-3 text-neutral-700 shadow-lg transition-all duration-200 hover:bg-primary-50 hover:text-primary-600 dark:bg-neutral-800 dark:text-neutral-300 dark:hover:bg-neutral-700"
                    >
                      <IconComponent className="h-4 w-4" />
                      <span className="font-medium">{page.name}</span>
                    </Link>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Help Text */}
          <motion.div
            className="mt-12 text-center"
            variants={animationVariants.item}
          >
            <p className="text-sm text-neutral-500 dark:text-neutral-500">
              Still can't find what you're looking for?{' '}
              <a
                href="mailto:<EMAIL>"
                className="text-primary-300 hover:underline"
              >
                Contact our support team
              </a>
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default NotFoundPage;
