'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'primary' | 'secondary' | 'white';
  text?: string;
  className?: string;
  fullScreen?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'primary',
  text,
  className,
  fullScreen = false,
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16',
  };

  const variantClasses = {
    primary: 'border-primary-300',
    secondary: 'border-neutral-400',
    white: 'border-white',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
  };

  const SpinnerComponent = (
    <div className={cn('flex flex-col items-center justify-center space-y-4', className)}>
      {/* Main Spinner */}
      <motion.div
        className={cn(
          'relative',
          sizeClasses[size]
        )}
        animate={{ rotate: 360 }}
        transition={{
          duration: 1,
          repeat: Infinity,
          ease: 'linear',
        }}
      >
        {/* Outer Ring */}
        <div className={cn(
          'absolute inset-0 rounded-full border-2 opacity-20',
          variantClasses[variant]
        )} />
        
        {/* Animated Ring */}
        <motion.div
          className={cn(
            'absolute inset-0 rounded-full border-2 border-transparent border-t-current',
            variantClasses[variant]
          )}
          animate={{ rotate: 360 }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: 'linear',
          }}
        />
        
        {/* Inner Glow */}
        <motion.div
          className={cn(
            'absolute inset-1 rounded-full opacity-20',
            variant === 'primary' && 'bg-gradient-to-r from-primary-300 to-primary-400',
            variant === 'secondary' && 'bg-gradient-to-r from-neutral-400 to-neutral-500',
            variant === 'white' && 'bg-gradient-to-r from-white to-gray-100'
          )}
          animate={{
            scale: [0.8, 1, 0.8],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
      </motion.div>

      {/* Loading Text */}
      {text && (
        <motion.p
          className={cn(
            'font-medium',
            textSizeClasses[size],
            variant === 'primary' && 'text-neutral-700 dark:text-neutral-300',
            variant === 'secondary' && 'text-neutral-600 dark:text-neutral-400',
            variant === 'white' && 'text-white'
          )}
          animate={{
            opacity: [0.7, 1, 0.7],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        >
          {text}
        </motion.p>
      )}

      {/* Progress Dots */}
      <motion.div
        className="flex space-x-1"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.6 }}
      >
        {[0, 1, 2].map((index) => (
          <motion.div
            key={index}
            className={cn(
              'rounded-full',
              size === 'sm' && 'h-1 w-1',
              size === 'md' && 'h-1.5 w-1.5',
              size === 'lg' && 'h-2 w-2',
              size === 'xl' && 'h-2.5 w-2.5',
              variant === 'primary' && 'bg-primary-300',
              variant === 'secondary' && 'bg-neutral-400',
              variant === 'white' && 'bg-white'
            )}
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: index * 0.2,
              ease: 'easeInOut',
            }}
          />
        ))}
      </motion.div>
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/80 backdrop-blur-sm dark:bg-black/80">
        {SpinnerComponent}
      </div>
    );
  }

  return SpinnerComponent;
};

// Preset Loading Components
export const LoadingButton: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({ size = 'sm' }) => (
  <LoadingSpinner size={size} variant="white" className="py-1" />
);

export const LoadingCard: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <div className="flex min-h-[200px] items-center justify-center rounded-lg bg-neutral-50 dark:bg-neutral-800">
    <LoadingSpinner size="lg" text={text} />
  </div>
);

export const LoadingPage: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <div className="flex min-h-screen items-center justify-center">
    <LoadingSpinner size="xl" text={text} />
  </div>
);

export const LoadingOverlay: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <LoadingSpinner size="lg" text={text} fullScreen />
);

export default LoadingSpinner;
