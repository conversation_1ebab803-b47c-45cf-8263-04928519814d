import Image from 'next/image';
import React from 'react';

interface Event {
  id: number;
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  price: string;
  featured: boolean;
  countdown?: {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  };
}

const FeaturedEvent = () => {
  const event: Event = {
    id: 1,
    title: '10X Growth Conference 2025',
    description:
      'Transform your business and mindset with the most powerful growth strategies. Join thousands of entrepreneurs for 3 days of intensive training.',
    date: 'August 15, 2025',
    time: '9:00 AM - 6:00 PM',
    location: 'Las Vegas, NV',
    price: '$250',
    featured: true,
    countdown: {
      days: 27,
      hours: 21,
      minutes: 5,
      seconds: 52,
    },
  };
  return (
    <section className="mx-8 mt-5 mb-16 rounded-2xl p-4 shadow-lg md:mx-18 dark:bg-black dark:shadow-none">
      <div className="mb-8 text-center">
        <button
          className={`relative rounded-2xl bg-black/20 px-8 py-4 text-sm font-medium text-white shadow-[0_0_20px_rgba(255,255,255,0.1)] backdrop-blur-md transition-all duration-300 ease-in-out hover:bg-black/30 hover:shadow-lg hover:shadow-white/10 focus:ring-2 focus:ring-white/30 focus:outline-none active:scale-95 md:text-lg`}
        >
          <span className="relative z-10">Featured Event</span>

          {/* Gradient border effect */}
          <div className="pointer-events-none absolute inset-0 rounded-2xl border-2 border-transparent bg-gradient-to-r from-transparent via-neutral-800 to-transparent bg-clip-border" />

          {/* Top border - white to black gradient */}
          <div className="pointer-events-none absolute top-0 right-2 left-2 h-[1px] bg-gradient-to-r from-transparent via-neutral-700 to-transparent" />

          {/* Bottom border - black to white gradient */}
          <div className="pointer-events-none absolute right-2 bottom-0 left-2 h-[1px] bg-gradient-to-r from-transparent via-neutral-700 to-transparent" />

          {/* Left border gradient */}
          <div className="pointer-events-none absolute top-2 bottom-2 left-0 w-[1px] bg-gradient-to-t from-neutral-50 to-neutral-800" />

          {/* Right border gradient */}
          <div className="pointer-events-none absolute top-2 right-0 bottom-2 w-[1px] bg-gradient-to-b from-neutral-50 to-neutral-800" />

          {/* Subtle inner glow */}
          {/* <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" /> */}
        </button>
      </div>

      <div className="mx-auto max-w-4xl overflow-hidden rounded-3xl">
        <div className="text-neutral-1000 p-8 text-center md:p-12 dark:text-white">
          <h2 className="mb-4 text-xl leading-tight font-bold sm:mb-5 sm:text-2xl md:mb-6 md:text-3xl lg:text-4xl xl:text-5xl">
            {event.title}
          </h2>

          <p className="mx-auto mb-8 text-sm leading-relaxed text-neutral-600 sm:mb-10 sm:text-base md:mb-12 md:text-lg lg:text-xl dark:text-neutral-300">
            {event.description}
          </p>

          {/* Countdown Timer عمر حط تايمر اللي عمله في هوم هنا */}

          <div className="mb-12 flex flex-col items-center justify-center gap-6 sm:flex-row md:gap-12">
            <button className="border-t-primary-500 border-b-primary-500 via-primary-600 hover:via-primary-400 w-full min-w-[200px] cursor-pointer rounded-xl border border-r-0 border-l-0 bg-gradient-to-r from-transparent to-transparent px-8 py-3 text-xs font-medium text-white backdrop-blur-md transition-all duration-300 ease-in-out hover:scale-105 active:scale-95 sm:w-auto sm:min-w-[250px] sm:rounded-2xl sm:py-4 sm:text-sm md:px-12 md:py-5 md:text-base lg:px-16 lg:py-6 lg:text-lg xl:text-xl">
              Reserve My Spot - {event.price}
            </button>
            <div className="flex items-center gap-3 text-neutral-600 dark:text-neutral-300">
              <div className="bg-primary-300 rounded-full p-1">
                <Image
                  src="/assets/svgs/location.svg"
                  alt="Location"
                  width={24}
                  height={24}
                />
              </div>

              <span className="text-sm font-medium sm:text-base md:text-lg">
                {event.location}
              </span>
            </div>
            <div className="flex items-center gap-3 text-neutral-600 dark:text-neutral-300">
              <div className="bg-primary-300 rounded-full p-1">
                <Image
                  src="/assets/svgs/calendar.svg"
                  alt="Calendar"
                  width={24}
                  height={24}
                />
              </div>

              <span className="text-sm font-medium sm:text-base md:text-lg">
                {event.date}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedEvent;
