'use client';
import FormField from '@/components/molecules/FormField/FormField';
import { signIn } from '../../../store/slices/auth/authSlice';
import GeneralBTN from '@/components/blocks/button/GeneralBTN';
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import Link from 'next/link';
import { useState } from 'react';

const SigninModal = () => {
  const dispatch = useAppDispatch();
  const { loading, error } = useAppSelector((state) => state.auth);

  const [form, setForm] = useState({ email: '', password: '' });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSignIn = async (email: string, password: string) => {
    console.log('Sign-in successful:', { email, password });
    try {
      await dispatch(signIn({ email, password })).unwrap();

      // Handle successful sign-in (redirect, etc.)
    } catch (error) {
      // Error is already handled in the slice
      console.error('Sign-in failed:', error);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleSignIn(form.email, form.password);
  };

  if (loading) return <div>Loading...</div>;
  return (
    <>
      {error && <div className="error">{error}</div>}

      <div className="rounded-7 relative mx-auto flex w-full max-w-md flex-col bg-white px-8 py-12 dark:bg-black">
        <h1 className="mb-2 text-center text-2xl font-bold">Welcome black</h1>
        <p className="mb-4 text-center text-sm font-medium text-neutral-600">
          Log in to your account to continue.
        </p>
        <form className="mt-4 space-y-4" onSubmit={(e) => handleSubmit(e)}>
          <FormField
            label="Email Address"
            name="email"
            type="email"
            value={form.email}
            onChange={handleChange}
            placeholder="Enter your email address"
            required
          />

          <FormField
            label="Password"
            name="password"
            type="password"
            value={form.password}
            onChange={handleChange}
            placeholder="Enter your password"
            required
          />
          <Link
            href="/forget-password"
            className="text-bold flex justify-end text-sm text-neutral-600"
          >
            Forget password?
          </Link>
          {loading ? (
            <GeneralBTN
              text="Signing In..."
              size="100%"
              textColor="white"
              height="45px"
              disabled={true}
            />
          ) : (
            <GeneralBTN
              text="Sign In"
              size="100%"
              textColor="white"
              height="45px"
            />
          )}
          <p className="mt-4 text-base text-neutral-600">
            {`Don't have an account? `}
            <a
              href="/auth/sign-up"
              className="text-primary-300 font-bold hover:underline"
            >
              Sign Up
            </a>
          </p>
        </form>
      </div>
    </>
  );
};
export default SigninModal;
