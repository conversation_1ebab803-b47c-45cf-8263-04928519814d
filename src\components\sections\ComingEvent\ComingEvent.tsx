'use client';
import CountDown from '@/components/atoms/countDown UI/CountDown';
import GeneralBTN from '@/components/blocks/button/GeneralBTN';
import { useCountdown } from '@/hooks/useCountdown/useCountdown';
import Image from 'next/image';
import React from 'react';

const ComingEvent = () => {
  const event = {
    title: 'Featured Event: Iginte Your Power',
    description:
      "Join this transformative workshop where you'll learn powerful techniques to overcome limiting beliefs and develop a growth mindset",
    date: '2025-08-15',
  };
  const countdown = useCountdown(event.date);
  //   console.log(countdown);
  function formatSingleDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }
  return (
    <div className="z-10 mx-auto h-auto w-10/12">
      <div
        style={{
          borderBottomLeftRadius: '16px',
          borderTopLeftRadius: '16px',
        }}
        className="container flex items-center justify-center rounded-lg text-black shadow-md dark:text-neutral-100"
      >
        <div
          style={{
            borderTopLeftRadius: '16px',
            borderBottomLeftRadius: '16px',
          }}
          className="left w-full bg-neutral-100 py-7 ps-8 dark:bg-black"
        >
          <div className="up">
            <h1 className="title text-2xl font-bold">{event.title}</h1>
            <h4 className="date text-primary-300 py-3 font-semibold">
              {' '}
              {formatSingleDate(event.date)}
            </h4>
            <p className="desc">{event.description}</p>
          </div>
          <div className="count-dwon">
            <CountDown countdown={countdown} />
            <div className="flex justify-center">
              <GeneralBTN
                text="Register Now"
                size="150px"
                height="43px"
                textColor="white"
                className="my-3"
              />
            </div>
            {/* <button>Register Now</button> */}
          </div>
        </div>

        <div className="relative h-full w-full">
          <Image
            src="/assets/landing-page/Frame 90.webp"
            fill
            alt='Event Image ""'
          />
        </div>
      </div>
    </div>
  );
};

export default ComingEvent;
