export interface CountdownResult {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  totalSeconds: number;
  isExpired: boolean;
  formatted: string;
  formattedArabic: string;
}
export function getCountdown(targetDate: Date | string): CountdownResult {
  const now = new Date().getTime();
  const target = new Date(targetDate).getTime();
  const difference = target - now;

  // إذا التاريخ فات
  if (difference <= 0) {
    return {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
      totalSeconds: 0,
      isExpired: true,
      formatted: '00:00:00:00',
      formattedArabic: 'انتهى الوقت',
    };
  }

  // حساب الوقت المتبقي
  const days = Math.floor(difference / (1000 * 60 * 60 * 24));
  const hours = Math.floor(
    (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
  );
  const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((difference % (1000 * 60)) / 1000);
  const totalSeconds = Math.floor(difference / 1000);

  // تنسيق النتيجة
  const formatted = `${days.toString().padStart(2, '0')}:${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

  const formattedArabic = `${days} يوم، ${hours} ساعة، ${minutes} دقيقة، ${seconds} ثانية`;

  return {
    days,
    hours,
    minutes,
    seconds,
    totalSeconds,
    isExpired: false,
    formatted,
    formattedArabic,
  };
}
