@import 'tailwindcss';
@import 'swiper/css';
@import 'swiper/css/effect-coverflow';
@import 'swiper/css/pagination';

@variant dark (html.dark &);

@theme {
  /* Colors */
  --color-primary-100: #fac1c1;
  --color-primary-200: #f68484;
  --color-primary-300: #f14646;
  --color-primary-400: #ee1a1a;
  --color-primary-500: #c90f0f;
  --color-primary-600: #9c0c0c;
  --color-primary-700: #700808;
  --color-primary-10: #f146461a;
  --color-primary-20: #f1464633;
  --color-primary-30: #f146464d;
  --color-primary-40: #f1464666;
  --color-primary-50: #f1464680;

  --color-secondary-100: #e9eaec;
  --color-secondary-200: #d2d5da;
  --color-secondary-300: #bcc0c7;
  --color-secondary-400: #a6abb5;
  --color-secondary-500: #9097a2;
  --color-secondary-600: #7a8290;
  --color-secondary-700: #676e7b;
  --color-secondary-800: #555b65;
  --color-secondary-900: #424750;
  --color-secondary-1000: #30343a;
  --color-secondary-10: #bcc0c71a;
  --color-secondary-20: #bcc0c733;
  --color-secondary-30: #bcc0c74d;
  --color-secondary-40: #bcc0c766;
  --color-secondary-50: #bcc0c780;

  --color-neutral-100: #ffffff;
  --color-neutral-200: #e3e3e3;
  --color-neutral-300: #c6c6c6;
  --color-neutral-400: #aaaaaa;
  --color-neutral-500: #8e8e8e;
  --color-neutral-600: #717171;
  --color-neutral-700: #555555;
  --color-neutral-800: #393939;
  --color-neutral-900: #1c1c1c;
  --color-neutral-1000: #000000;
  --color-neutral-10: #0000001a;

  --color-red-100: #fb3748;
  --color-red-200: #d00416;
  --color-red-10: #fb37481a;

  --color-yellow-100: #ffdb43;
  --color-yellow-200: #dfb400;
  --color-yellow-10: #ffdb431a;

  --color-green-100: #84ebb4;
  --color-green-200: #1fc16b;
  --color-green-10: #1fc16b1a;

  /* Spacing */
  --spacing-xs: 2px;
  --spacing-s: 4px;
  --spacing-sm: 8px;
  --spacing-m: 12px;
  --spacing-ml: 16px;
  --spacing-l: 20px;
  --spacing-xl: 24px;
  --spacing-2-xl: 32px;
  --spacing-3-xl: 40px;
  --spacing-4-xl: 48px;
  --spacing-5-xl: 56px;

  /* Border Radius */
  --radius-0: 0px;
  --radius-1: 4px;
  --radius-1-5: 6px;
  --radius-2: 8px;
  --radius-3: 12px;
  --radius-4: 16px;
  --radius-5: 20px;
  --radius-6: 24px;
  --radius-7: 32px;
  --radius-8: 999px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  min-height: 100dvh;
  overflow-x: hidden;
}

:root {
  --color-primary-300: #f14646;
}

/* Premium Global Custom Scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary-300) transparent;
}

*::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

*::-webkit-scrollbar-track {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.02) 0%,
    rgba(255, 255, 255, 0.08) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
  border-radius: 50px;
  margin: 4px 0;
  border: 1px solid rgba(255, 255, 255, 0.03);
}

*::-webkit-scrollbar-thumb {
  background: linear-gradient(
    45deg,
    var(--color-primary-300) 0%,
    #ff6b6b 50%,
    #ee5a24 100%
  );
  border-radius: 50px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 2px 10px rgba(241, 70, 70, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

*::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #ff5252 0%, #ff7979 50%, #fd79a8 100%);
  transform: scale(1.05);
  box-shadow:
    0 4px 20px rgba(241, 70, 70, 0.5),
    0 0 15px rgba(255, 82, 82, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

*::-webkit-scrollbar-thumb:active {
  background: linear-gradient(45deg, #d63031 0%, #e84393 50%, #a29bfe 100%);
  transform: scale(0.95);
  box-shadow:
    0 2px 8px rgba(241, 70, 70, 0.4),
    inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

*::-webkit-scrollbar-corner {
  background: transparent;
  border-radius: 50px;
}

/* Special hover effects for body scrollbar */
body::-webkit-scrollbar-thumb {
  background: linear-gradient(
    180deg,
    var(--color-primary-300) 0%,
    #ff6b6b 30%,
    #ee5a24 70%,
    #d63031 100%
  );
  box-shadow:
    0 0 15px rgba(241, 70, 70, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    180deg,
    #ff5252 0%,
    #ff7979 30%,
    #fd79a8 70%,
    #e84393 100%
  );
  box-shadow:
    0 0 25px rgba(241, 70, 70, 0.4),
    0 0 40px rgba(255, 82, 82, 0.2),
    inset 0 1px 2px rgba(255, 255, 255, 0.3);
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  *::-webkit-scrollbar-track {
    background: linear-gradient(
      90deg,
      rgba(0, 0, 0, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(0, 0, 0, 0.1) 100%
    );
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  *::-webkit-scrollbar-thumb {
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow:
      0 2px 10px rgba(241, 70, 70, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }

  * {
    scrollbar-color: var(--color-primary-300) rgba(255, 255, 255, 0.05);
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom pulse animation for scrollbar */
@keyframes scrollbar-pulse {
  0%,
  100% {
    box-shadow: 0 0 15px rgba(241, 70, 70, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(241, 70, 70, 0.6);
  }
}

/* Add pulse effect on page load */
*::-webkit-scrollbar-thumb {
  animation: scrollbar-pulse 2s ease-in-out infinite;
}

*::-webkit-scrollbar-thumb:hover {
  animation: none;
}

html {
  scroll-behavior: smooth;
}
