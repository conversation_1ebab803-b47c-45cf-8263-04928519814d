'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Alert<PERSON>riangle, Bug, Zap } from 'lucide-react';
import GeneralBTN from '@/components/blocks/button/GeneralBTN';

const ErrorTrigger: React.FC = () => {
  const [shouldThrowError, setShouldThrowError] = useState(false);

  // This will trigger the error boundary
  if (shouldThrowError) {
    throw new Error('This is a test error to demonstrate the error page!');
  }

  const triggerError = () => {
    setShouldThrowError(true);
  };

  const triggerAsyncError = async () => {
    // Simulate an async error
    throw new Error('This is an async error!');
  };

  const triggerNetworkError = () => {
    // Simulate a network error
    fetch('/non-existent-endpoint')
      .then(() => {
        // This won't be reached
      })
      .catch((error) => {
        console.error('Network error:', error);
        // This won't trigger the error boundary, but will show in console
      });
  };

  return (
    <motion.div
      className="mx-auto max-w-2xl rounded-2xl bg-white p-8 shadow-xl dark:bg-neutral-800"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="text-center">
        <motion.div
          className="mb-6 flex justify-center"
          animate={{
            rotate: [0, 5, -5, 0],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        >
          <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900 dark:to-red-800">
            <Bug className="h-8 w-8 text-red-600 dark:text-red-400" />
          </div>
        </motion.div>

        <h2 className="mb-4 text-2xl font-bold text-neutral-800 dark:text-neutral-200">
          Error Testing Component
        </h2>

        <p className="mb-8 text-neutral-600 dark:text-neutral-400">
          Use these buttons to test different error scenarios and see how the error pages work.
        </p>

        <div className="space-y-4">
          {/* Trigger Error Boundary */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <GeneralBTN
              text="Trigger Error Boundary"
              size="100%"
              height="50px"
              bg="red-500"
              textColor="white"
              onClick={triggerError}
              className="flex items-center justify-center gap-2"
            />
          </motion.div>

          {/* Trigger Async Error */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <GeneralBTN
              text="Trigger Async Error"
              size="100%"
              height="50px"
              bg="orange-500"
              textColor="white"
              onClick={triggerAsyncError}
              className="flex items-center justify-center gap-2"
            />
          </motion.div>

          {/* Trigger Network Error */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <GeneralBTN
              text="Trigger Network Error"
              size="100%"
              height="50px"
              bg="yellow-500"
              textColor="white"
              onClick={triggerNetworkError}
              className="flex items-center justify-center gap-2"
            />
          </motion.div>
        </div>

        {/* Instructions */}
        <motion.div
          className="mt-8 rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <div className="flex items-start gap-3">
            <AlertTriangle className="mt-0.5 h-5 w-5 text-blue-600 dark:text-blue-400" />
            <div className="text-left">
              <h3 className="font-semibold text-blue-800 dark:text-blue-300">
                How to test errors:
              </h3>
              <ul className="mt-2 space-y-1 text-sm text-blue-700 dark:text-blue-400">
                <li>• <strong>Error Boundary:</strong> Will show the global error page</li>
                <li>• <strong>Async Error:</strong> Check browser console for error</li>
                <li>• <strong>Network Error:</strong> Check network tab in DevTools</li>
                <li>• <strong>404 Error:</strong> Visit <code>/non-existent-page</code></li>
              </ul>
            </div>
          </div>
        </motion.div>

        {/* Additional Test Links */}
        <motion.div
          className="mt-6 space-y-2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.7 }}
        >
          <p className="text-sm text-neutral-600 dark:text-neutral-400">
            Quick test links:
          </p>
          <div className="flex flex-wrap justify-center gap-2">
            <a
              href="/non-existent-page"
              className="rounded-full bg-neutral-100 px-4 py-2 text-sm text-neutral-700 transition-colors hover:bg-neutral-200 dark:bg-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-600"
            >
              Test 404 Page
            </a>
            <a
              href="/admin/non-existent"
              className="rounded-full bg-neutral-100 px-4 py-2 text-sm text-neutral-700 transition-colors hover:bg-neutral-200 dark:bg-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-600"
            >
              Test Admin 404
            </a>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default ErrorTrigger;
