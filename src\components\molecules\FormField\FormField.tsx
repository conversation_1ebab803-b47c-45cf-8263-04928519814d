// components/molecules/FormField.tsx
import React from 'react';

interface FormFieldProps {
  label: string;
  name: string;
  type?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  required?: boolean;
}

const FormField: React.FC<FormFieldProps> = ({
  label,
  name,
  type = 'text',
  value,
  onChange,
  placeholder,
  required = false,
}) => {
  return (
    <div className="font-size-sm flex flex-col gap-1.5">
      <label
        htmlFor={name}
        className="block text-base font-medium text-black dark:text-white"
      >
        {label}
        {required && <span className="text-primary-200 ml-1">*</span>}
      </label>
      <input
        id={name}
        name={name}
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        required={required}
        className="rounded-1-5 py-s px-m focus:border-primary-200 focus:ring-primary-200 block w-full border border-[#0d0d0d] shadow-sm outline-none placeholder:text-neutral-600 focus:ring-1"
      />
    </div>
  );
};

export default FormField;
