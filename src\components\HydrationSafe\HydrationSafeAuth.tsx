'use client';

import React, { useState, useEffect } from 'react';

interface HydrationSafeAuthProps {
  children: (props: {
    isHydrated: boolean;
    user: any;
    loading: boolean;
    error: string | null;
    markAsLoggedOut: () => void;
    showSessionExpiredToast: boolean;
    setShowSessionExpiredToast: (show: boolean) => void;
  }) => React.ReactNode;
  fallback?: React.ReactNode;
}

const HydrationSafeAuth: React.FC<HydrationSafeAuthProps> = ({ 
  children, 
  fallback = null 
}) => {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // During SSR and before hydration, show fallback or loading state
  if (!isHydrated) {
    return (
      <>
        {fallback || children({
          isHydrated: false,
          user: null,
          loading: true,
          error: null,
          markAsLoggedOut: () => {},
          showSessionExpiredToast: false,
          setShowSessionExpiredToast: () => {},
        })}
      </>
    );
  }

  // After hydration, dynamically import and use the real useAuth hook
  return <HydratedAuthContent>{children}</HydratedAuthContent>;
};

// Separate component that only runs after hydration
const HydratedAuthContent: React.FC<{
  children: (props: {
    isHydrated: boolean;
    user: any;
    loading: boolean;
    error: string | null;
    markAsLoggedOut: () => void;
    showSessionExpiredToast: boolean;
    setShowSessionExpiredToast: (show: boolean) => void;
  }) => React.ReactNode;
}> = ({ children }) => {
  // Dynamic import to avoid SSR issues
  const [authHook, setAuthHook] = useState<any>(null);

  useEffect(() => {
    import('@/hooks/useAuth').then((module) => {
      setAuthHook(() => module.useAuth);
    });
  }, []);

  // If hook not loaded yet, show loading state
  if (!authHook) {
    return (
      <>
        {children({
          isHydrated: true,
          user: null,
          loading: true,
          error: null,
          markAsLoggedOut: () => {},
          showSessionExpiredToast: false,
          setShowSessionExpiredToast: () => {},
        })}
      </>
    );
  }

  // Use the actual auth hook
  const authData = authHook();

  return (
    <>
      {children({
        isHydrated: true,
        ...authData,
      })}
    </>
  );
};

export default HydrationSafeAuth;
