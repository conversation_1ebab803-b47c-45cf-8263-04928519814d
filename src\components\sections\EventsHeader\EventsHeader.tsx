import React from 'react';
import Image from 'next/image';

const EventsHeader = () => {
  return (
    <section className="relative mx-8 mb-16 flex h-[400px] items-center justify-center overflow-hidden rounded-3xl text-center md:mx-18">
      <Image
        src="/assets/events/header-bg.webp"
        alt="Events Header Background"
        fill
        className="absolute inset-0 w-full object-center"
        priority
      />
      <div className="absolute inset-0 rounded-3xl bg-black/60" />
      <div className="relative z-10 max-w-4xl px-8">
        <h1 className="mb-4 text-2xl leading-tight font-bold text-white sm:mb-6 sm:text-3xl sm:leading-snug md:text-4xl lg:text-5xl xl:text-[54px]">
          Unlock Your Potential – Join the{' '}
          <span className="text-primary-300">Next Live Event</span>
        </h1>

        <p className="mx-auto mb-6 max-w-2xl text-sm leading-relaxed text-neutral-200 sm:mb-8 sm:text-base md:text-lg lg:text-xl">
          Transform your business, mindset, and life with world-class training
          from industry leaders.
        </p>

        <button className="border-t-primary-500 border-b-primary-500 via-primary-400/20 hover:via-primary-400 after:bg-primary-400 relative z-[1] cursor-pointer rounded-xl border border-r-0 border-l-0 bg-gradient-to-r from-transparent from-10% via-50% to-transparent to-90% px-6 py-2 text-sm font-medium text-white backdrop-blur-md transition-all duration-300 ease-in-out after:absolute after:-top-1 after:left-1/2 after:-z-0 after:h-1 after:w-4 after:-translate-x-1/2 after:rounded-xl after:transition-all after:duration-300 after:content-[''] hover:scale-105 hover:bg-gradient-to-r hover:from-transparent hover:to-transparent hover:text-base hover:after:bg-transparent active:scale-95 sm:rounded-2xl sm:px-8 sm:py-3 sm:text-base sm:after:h-2 sm:after:w-6 sm:after:rounded-2xl sm:hover:text-lg md:px-12 md:py-4 md:text-lg md:hover:text-xl lg:px-14 lg:text-xl lg:hover:text-2xl">
          <a href="#upcoming-events"> View Upcoming Events</a>
        </button>
      </div>
    </section>
  );
};

export default EventsHeader;
