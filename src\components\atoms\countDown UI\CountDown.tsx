'use client';
import React from 'react';

interface Props {
  countdown: {
    days: number;
    seconds: number;
    minutes: number;
    hours: number;
  };
}

function CountDown(props: Props) {
  const { countdown } = props;

  return (
    <div className="countDwon mx-auto my-6 flex justify-center gap-4">
      <div>
        <div className="day rounded-md border-2 border-[#F2F2F2] p-2 text-center font-bold shadow-lg">
          {countdown.days}
        </div>
        <p className="text-neutral-600"> Days</p>
      </div>
      <div>
        <div className="hours rounded-md border-2 border-[#F2F2F2] p-2 text-center font-bold shadow-lg">
          {countdown.hours}
        </div>
        <p className="text-neutral-600">Hours</p>
      </div>
      <div>
        <div className="minute rounded-md border-2 border-[#F2F2F2] p-2 text-center font-bold shadow-lg">
          {countdown.minutes}
        </div>
        <p className="text-neutral-600">Minutes</p>
      </div>
      <div>
        <div className="seconds rounded-md border-2 border-[#F2F2F2] p-2 text-center font-bold shadow-lg">
          {countdown.seconds}
        </div>
        <p className="text-neutral-600">Seconds</p>
      </div>
    </div>
  );
}

export default CountDown;
