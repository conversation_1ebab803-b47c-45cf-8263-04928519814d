import React from 'react';

interface TextHighlightProps {
  paragraphs: string[];
  className?: string;
}

const TextHighlight: React.FC<TextHighlightProps> = ({
  paragraphs,
  className = '',
}) => {
  return (
    <div
      className={`py-4-xl gap-4-xl rounded-6 my-3 flex flex-col px-6 text-center shadow-2xl md:gap-2 dark:shadow-none ${className}`}
    >
      {paragraphs.map((paragraph, index) => (
        <p
          key={index}
          className="text-md leading-relaxed text-neutral-600 dark:text-neutral-300"
        >
          {paragraph}
        </p>
      ))}
    </div>
  );
};

export default TextHighlight;
