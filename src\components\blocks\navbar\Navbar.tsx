'use client';
import Image from 'next/image';
import Link from 'next/link';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Menu, X, Settings, LogOut, ChevronDown, ShoppingCart, Bell } from 'lucide-react';
import GeneralBTN from '../button/GeneralBTN';
import ColorModeSwitch from '@/components/atoms/ColorMoreSwitch/ColorModeSwitch';
import { useAppDispatch } from '@/hooks/reduxHooks';
import { logout } from '@/store/slices/auth/authSlice';
import { useAuth } from '@/hooks/useAuth';
import Toast from '@/components/Toast/Toast';

function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  const dispatch = useAppDispatch();
  const { user, loading, error, showSessionExpiredToast, setShowSessionExpiredToast } = useAuth();

  const btnLinks = [
    {
      name: 'Home',
      path: '/',
    },
    {
      name: 'About',
      path: '/about-us',
    },
    {
      name: 'Events',
      path: '/events',
    },
    {
      name: 'Store',
      path: '#',
    },
    {
      name: 'Contact',
      path: '#',
    },
  ];

  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
      setIsUserDropdownOpen(false);
    }
    if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node)) {
      setIsMobileMenuOpen(false);
    }
  }, []);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      setIsUserDropdownOpen(false);
      setIsMobileMenuOpen(false);
    }
  }, []);

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleClickOutside, handleKeyDown]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
    setIsUserDropdownOpen(false);
  };

  const toggleUserDropdown = () => {
    setIsUserDropdownOpen(!isUserDropdownOpen);
    setIsMobileMenuOpen(false);
  };

  const handleLogout = async () => {
    try {
      await dispatch(logout()).unwrap();
      setIsUserDropdownOpen(false);
      setIsMobileMenuOpen(false);

      // Workaround: Force page reload to clear any cached state
      // This ensures cookies are properly handled on next request
      setTimeout(() => {
        window.location.href = '/auth/sign-in';
      }, 500);
    } catch (error) {
      console.error('Logout failed:', error);
      setIsUserDropdownOpen(false);
      setIsMobileMenuOpen(false);

      // Even if logout fails, redirect to login
      setTimeout(() => {
        window.location.href = '/auth/sign-in';
      }, 500);
    }
  };

  return (
    <div className="relative">
      {/* Error Display */}
      {error && (
        <div className="mx-auto mt-2 w-10/12 rounded-lg bg-red-100 px-4 py-2 text-sm text-red-700 dark:bg-red-900 dark:text-red-200">
          {error}
        </div>
      )}

      <div
        style={{ borderRadius: '24px' }}
        className="z-50 mx-auto mt-3 flex w-10/12 items-center justify-between bg-neutral-100 px-5 py-3 text-black shadow-2xl dark:bg-neutral-900 dark:text-neutral-100"
      >
        <div className="flex items-center">
          <Image
            src="/assets/landing-page/blackLogo.svg"
            alt="logo"
            width={50}
            height={40}
            className="dark:hidden"
          />
          <Image
            src="/assets/landing-page/logo.svg"
            alt="logo"
            width={50}
            height={40}
            className="hidden dark:block"
          />
        </div>

        <div className="hidden lg:block">
          <ul className="flex">
            {btnLinks?.map((el) => (
              <li className="px-3 font-semibold" key={el.name}>
                <Link
                  href={el.path}
                  className="transition-colors duration-200 hover:text-primary-400"
                >
                  {el.name}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        <div className="flex items-center space-x-2">
          {user ? (
            <>
              <ColorModeSwitch />
              <button
                className="hidden md:flex transform rounded-xl p-3 shadow-lg transition-all duration-300 focus:outline-none
                 border border-neutral-700 bg-slate-800 hover:bg-slate-900 cursor-pointer dark:bg-transparent "
                aria-label="Shopping Cart"
              >
                <Image src="/assets/Svgs/shopping-cart.svg" alt="cart"  width={18} height={18} className='invert' />
              </button>

              <button
                className="hidden md:flex transform rounded-full p-3 shadow-lg transition-all duration-300 focus:outline-none
                  bg-gradient-to-br hover:shadow-xl
                  from-slate-700 to-slate-900 hover:from-slate-600 hover:to-slate-800"
                aria-label="Notifications"
              >
                <Image src="/assets/Svgs/notification.svg" alt="bell" width={18} height={18} className='invert' />
              </button>



              <div className="relative" ref={dropdownRef}>
                <button
                  onClick={toggleUserDropdown}
                  className="flex items-center space-x-2 rounded-full p-1 transition-colors duration-200 hover:bg-neutral-200 focus:outline-none dark:hover:bg-neutral-700"
                  aria-expanded={isUserDropdownOpen}
                  aria-haspopup="true"
                >
                  <Image
                    src={user?.profileImage}
                    alt="user profile"
                    width={40}
                    height={40}
                    className="rounded-full"
                  />
                  <span className="hidden md:block font-semibold">{user?.name}</span>
                  <ChevronDown
                    size={16}
                    className={`transition-transform duration-200 ${
                      isUserDropdownOpen ? 'rotate-180' : ''
                    }`}
                  />
                </button>

                {isUserDropdownOpen && (
                  <div className="absolute right-0 mt-2 w-48 rounded-lg bg-white shadow-lg ring-1 ring-black ring-opacity-5 dark:bg-neutral-800 dark:ring-neutral-700">
                    <div className="py-1" role="menu" aria-orientation="vertical">
                      <Link
                        href="/settings"
                        className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-neutral-700"
                        role="menuitem"
                        onClick={() => setIsUserDropdownOpen(false)}
                      >
                        <Settings size={16} className="mr-3" />
                        Settings
                      </Link>
                      <button
                        onClick={handleLogout}
                        disabled={loading}
                        className={`flex w-full items-center px-4 py-2 text-sm ${
                          loading
                            ? 'text-gray-400 cursor-not-allowed'
                            : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-neutral-700'
                        }`}
                        role="menuitem"
                      >
                        <LogOut size={16} className="mr-3" />
                        {loading ? 'Logging out...' : 'Logout'}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </>
          ) : (
            <>
              <ColorModeSwitch />

              <div className="hidden md:flex items-center">
                <GeneralBTN
                  text="Log In"
                  size="150px"
                  textColor="primary-300"
                  bg="transparent"
                  margin="m-2"
                  border="1"
                  height={'35px'}
                  href="/auth/sign-in"
                />
                <GeneralBTN
                  text="Sign Up"
                  size="150px"
                  bg="primary-300"
                  textColor="white"
                  height={'35px'}
                  href="/auth/sign-up"
                />
              </div>
            </>
          )}

          <button
            onClick={toggleMobileMenu}
            className="lg:hidden rounded-md p-2 text-gray-600 hover:bg-gray-100 hover:text-gray-900 focus:outline-none dark:text-gray-300 dark:hover:bg-neutral-700 dark:hover:text-white"
            aria-expanded={isMobileMenuOpen}
            aria-label="Toggle mobile menu"
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {isMobileMenuOpen && (
        <div
          ref={mobileMenuRef}
          className="absolute top-full left-0 right-0 z-40 mx-auto mt-2 w-10/12 rounded-2xl bg-neutral-100 shadow-2xl dark:bg-neutral-900"
          style={{ borderRadius: '24px' }}
        >
          <div className="px-5 py-4">
            <div className="space-y-3 border-b border-neutral-200 pb-4 dark:border-neutral-700">
              {btnLinks?.map((el) => (
                <Link
                  key={el.name}
                  href={el.path}
                  className="block px-3 py-2 font-semibold transition-colors duration-200 hover:text-primary-400"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {el.name}
                </Link>
              ))}
            </div>

            {!user && (
              <div className="mt-4 space-y-3">
                <GeneralBTN
                  text="Log In"
                  size="100%"
                  textColor="primary-300"
                  bg="transparent"
                  border="1"
                  height={'40px'}
                  href="/auth/sign-in"
                />
                <GeneralBTN
                  text="Sign Up"
                  size="100%"
                  bg="primary-300"
                  textColor="white"
                  height={'40px'}
                  href="/auth/sign-up"
                />
              </div>
            )}

            {user && (
              <div className="mt-4 space-y-2">
                <Link
                  href="/cart"
                  className="flex w-full items-center px-3 py-2 text-sm font-semibold transition-colors duration-200 hover:text-primary-400"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <ShoppingCart size={16} className="mr-3" />
                  Cart
                </Link>
                <Link
                  href="/notifications"
                  className="flex w-full items-center px-3 py-2 text-sm font-semibold transition-colors duration-200 hover:text-primary-400"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Bell size={16} className="mr-3" />
                  Notifications
                </Link>
                <Link
                  href="/settings"
                  className="flex w-full items-center px-3 py-2 text-sm font-semibold transition-colors duration-200 hover:text-primary-400"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Settings size={16} className="mr-3" />
                  Settings
                </Link>
                <button
                  onClick={handleLogout}
                  disabled={loading}
                  className={`flex w-full items-center px-3 py-2 text-sm font-semibold transition-colors duration-200 ${
                    loading
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'hover:text-primary-400'
                  }`}
                >
                  <LogOut size={16} className="mr-3" />
                  {loading ? 'Logging out...' : 'Logout'}
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Session Expiration Toast */}
      <Toast
        message="Your session has expired. Redirecting to login..."
        type="warning"
        show={showSessionExpiredToast}
        duration={3000}
        onClose={() => setShowSessionExpiredToast(false)}
      />
    </div>
  );
}

export default Navbar;
