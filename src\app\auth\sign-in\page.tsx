'use client';
import SigninModal from '@/components/blocks/signinModal/SigninModal';
import { useAppSelector } from '@/hooks/reduxHooks';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

const SignIn = () => {
  const { user } = useAppSelector((state) => state.auth);
  const router = useRouter();

  useEffect(() => {
    if (user) {
      router.push('/');
    }
  }, [user, router]);

  return <>{!user && <SigninModal />}</>;
};

export default SignIn;
