'use client';

import React, { Component, ReactNode } from 'react';
import { motion } from 'framer-motion';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import GeneralBTN from '@/components/blocks/button/GeneralBTN';
import Image from 'next/image';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return <ErrorFallback error={this.state.error} onReset={this.handleReset} />;
    }

    return this.props.children;
  }
}

interface ErrorFallbackProps {
  error?: Error;
  onReset: () => void;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, onReset }) => {
  const animationVariants = {
    container: {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          staggerChildren: 0.2,
          delayChildren: 0.1,
        },
      },
    },
    item: {
      hidden: { opacity: 0, y: 20 },
      visible: {
        opacity: 1,
        y: 0,
        transition: { duration: 0.6, ease: 'easeOut' },
      },
    },
  };

  return (
    <div className="flex min-h-[400px] items-center justify-center rounded-lg bg-gradient-to-br from-neutral-50 to-neutral-100 p-8 dark:from-neutral-800 dark:to-neutral-900">
      <motion.div
        className="w-full max-w-md text-center"
        variants={animationVariants.container}
        initial="hidden"
        animate="visible"
      >
        {/* Logo */}
        <motion.div
          className="mb-6 flex justify-center"
          variants={animationVariants.item}
        >
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-white shadow-lg dark:bg-neutral-700">
            <Image
              src="/assets/landing-page/blackLogo.svg"
              alt="Personal Coach Logo"
              width={24}
              height={24}
              className="dark:hidden"
            />
            <Image
              src="/assets/landing-page/logo.svg"
              alt="Personal Coach Logo"
              width={24}
              height={24}
              className="hidden dark:block"
            />
          </div>
        </motion.div>

        {/* Error Icon */}
        <motion.div
          className="mb-6 flex justify-center"
          variants={animationVariants.item}
        >
          <motion.div
            className="relative"
            animate={{
              scale: [1, 1.05, 1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          >
            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-red-100 to-red-200 shadow-lg dark:from-red-900 dark:to-red-800">
              <AlertTriangle className="h-8 w-8 text-red-600 dark:text-red-400" />
            </div>
          </motion.div>
        </motion.div>

        {/* Error Message */}
        <motion.div
          className="mb-6 space-y-3"
          variants={animationVariants.item}
        >
          <h3 className="text-xl font-bold text-neutral-800 dark:text-neutral-200">
            Something went wrong
          </h3>
          
          <p className="text-sm text-neutral-600 dark:text-neutral-400">
            We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.
          </p>

          {/* Error Details (Development) */}
          {process.env.NODE_ENV === 'development' && error && (
            <motion.div
              className="mt-4 rounded-lg bg-red-50 p-3 text-left dark:bg-red-900/20"
              variants={animationVariants.item}
            >
              <h4 className="mb-1 text-xs font-semibold text-red-800 dark:text-red-400">
                Error Details:
              </h4>
              <p className="text-xs text-red-700 dark:text-red-300">
                {error.message}
              </p>
            </motion.div>
          )}
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          className="flex flex-col gap-3 sm:flex-row sm:justify-center"
          variants={animationVariants.item}
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <GeneralBTN
              text="Try Again"
              size="140px"
              height="40px"
              bg="primary-300"
              textColor="white"
              onClick={onReset}
              className="flex items-center justify-center gap-2"
            />
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <GeneralBTN
              text="Go Home"
              size="140px"
              height="40px"
              bg="transparent"
              textColor="primary-300"
              border="1"
              href="/"
              className="flex items-center justify-center gap-2 border-primary-300"
            />
          </motion.div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default ErrorBoundary;
