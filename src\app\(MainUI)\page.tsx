// // import ColorModeSwitch from '@/components/atoms/ColorMoreSwitch/ColorModeSwitch';
// import ProductCard from '@/components/blocks/bestProducts/ProductCard';
// import GeneralBTN from '@/components/blocks/button/GeneralBTN';
// import ClientSays from '@/components/blocks/clientSyas/ClientSays';
// import IconCards from '@/components/blocks/IconCards/IconCards';
// import SectionHeading from '@/components/molecules/SectionHeading';
// import ComingEvent from '@/components/sections/ComingEvent/ComingEvent';
// import Footer from '@/components/sections/footer/Footer';
// import StartNowSection from '@/components/sections/StartNowSection/StartNowSection';
// // import Image from 'next/image';

// export default function Home() {
//   const corePhilosophyCards = [
//     {
//       icon: '/assets/svgs/heart.svg',
//       title: 'Clarity blooms from struggle',
//       description:
//         'Your greatest challenges contain the seeds of your greatest breakthroughs. I help you transform pain into purpose and obstacles into opportunities. ',
//     },
//     {
//       icon: '/assets/svgs/goals.svg',
//       title: 'Action creates momentum',
//       description:
//         "Transformation requires courage to face the truth about where you are and where you want to go. We'll have the conversations that matter most.",
//     },
//     {
//       icon: '/assets/svgs/mind.svg',
//       title: 'Growth requires courage',
//       description:
//         "Purpose isn't something you discover lying around—it's something you create through aligned action, clear values, and consistent commitment to growth. ",
//     },]

//   const bestProducts = [
//     {
//       title: 'eBook: The Clarity Code',
//       price:
//       '$29.99',
//       img: '/assets/landing-page/Frame 99-2.webp',
//     },

//     {
//       title: 'eBook: The Clarity Code',
//       price:
//       '$29.99',
//       img: '/assets/landing-page/Frame 99-1.webp',
//     },

//     {
//       title: 'eBook: The Clarity Code',
//       price:
//       '$29.99',
//       img: '/assets/landing-page/Frame 99.webp',
//     },

//   ];
//   const clientSays = [
//     {
//       say: "Working with Coach Morgan completely transformed my approach to leadership. I've never felt more confident in my abilities",
//       name:
//       'Sarah Johnson',
//       job: 'CEO, TechStart Inc.',
//     },
//     {
//       say: "Working with Coach Morgan completely transformed my approach to leadership. I've never felt more confident in my abilities..",
//       name:
//       'Sarah Johnson',
//       job: 'CEO, TechStart Inc.',
//     },
//     {
//       say: "Working with Coach Morgan completely transformed my approach to leadership. I've never felt more confident in my abilities.",
//       name:
//       'Sarah Johnson',
//       job: 'CEO, TechStart Inc.',
//     },

//   ];
//   return (
//     <div className="bg-white dark:bg-black">
//       <div className="relative h-screen w-full overflow-hidden">
//         {/* الفيديو كخلفية */}
//         <video
//           autoPlay
//           muted
//           loop
//           playsInline
//           className="absolute top-0 left-0 z-0 h-full w-full object-cover"
//           poster="/fallback.jpg"
//         >
//           <source
//             src="/Videos/hero-section-high.mp4"
//             type="video/mp4"
//             media="(min-width: 1024px)"
//           />
//           <source
//             src="/public/Videos/hero-section-low.mp4"
//             type="video/mp4"
//             media="(min-width: 768px)"
//           />
//           <source src="/hero-mobile.mp4" type="video/mp4" />
//           Your browser does not support the video tag.
//         </video>

//         {/* المحتوى فوق الفيديو */}
//         <div className="absolute z-10 flex h-full w-full items-center justify-center bg-black text-white opacity-60"></div>
//         <div className="relative z-20 flex h-full items-center justify-center bg-transparent text-white opacity-80">
//           <div>
//             <div>
//               <h1 className="line text-center text-4xl leading-normal font-bold">
//                 You’re One Decision Away <br />
//                 from a Better Life{' '}
//               </h1>

//               <br />

//               <p className="text-center">
//                 Whether you’re feeling stuck or ready for more, this is your
//                 moment. Let’s build the mindset, <br />
//                 the plan, and the momentum together.
//               </p>
//             </div>
//             <div className="px-auto mt-3 flex gap-3">
//               <div className="mx-auto">
//                 <GeneralBTN
//                   text="Book a Session "
//                   size={'400px'}
//                   height={'35px'}
//                 />
//               </div>
//               <div className="mx-auto">
//                 <GeneralBTN
//                   text="Log In"
//                   size="400px"
//                   textColor=""
//                   bg="transparent"
//                   margin="m-2"
//                   border="1"
//                   height={'35px'}
//                 />
//                 {/* <GeneralBTN text="Book a Session " size="420px" height={"35px"}  /> */}
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>

//       {/* second section */}
//       <div className="define my-10 mb-[80px] h-auto">
//         <div className="flex flex-col items-center justify-center">
//           <SectionHeading
//             headline="Hi, I’m Mahmoud"
//             subLine="With over 15 years of experience in personal development and business coaching, I'm passionate about helping ambitious individuals break through their limitations and create extraordinary results in their lives and careers."
//           />
//           <div className=" ">
//             <GeneralBTN
//               text="Explore Programs"
//               border="1"
//               size="327px"
//               height="43px"
//               bg="#F8F9FA"
//               textColor="black"
//             />
//           </div>
//         </div>
//       </div>
//       {/* What I Offer */}

//       <div className="my-10 mb-15 h-auto">
//         <div className="flex flex-col items-center justify-center">
//           <h2 className="text-neutral-1000 mb-ml text-3xl font-bold md:text-4xl dark:text-neutral-100">
//             What I Offer
//           </h2>
//         </div>
//         <div className="mx-20 mt-10">
//           <IconCards cards={corePhilosophyCards} columns={3} />
//         </div>
//       </div>

//       {/* Coming Event */}
//       <div>
//         <div className="w-wv relative container flex items-center justify-center p-10 py-20">
//           <div className="absolute left-[-10%] z-0 h-96 w-96 rounded-full border-2 bg-gradient-to-r from-[#f6caca] via-white to-[#e8b1b1] blur-lg dark:bg-[#F14646] dark:bg-none dark:opacity-10"></div>
//           <div className="absolute right-[-10%] z-0 h-96 w-96 rounded-full bg-gradient-to-r from-[#dd8d8d] via-white to-[#e8b1b1] blur-lg dark:bg-[#F14646] dark:bg-none dark:opacity-10"></div>

//           <ComingEvent />
//         </div>
//       </div>

//       {/* Best-Selling Products */}
//       <div className=''>
//         <div className="flex flex-col items-center justify-center">
//           <h2 className="text-neutral-1000 mb-ml text-3xl font-bold md:text-4xl dark:text-neutral-100">
//             Best-Selling Products
//           </h2>
//         </div>
//         <div className=' flex flex-wrap gap-5 justify-center  py-10'>
//               {bestProducts?.map((product)=>(
//                   <ProductCard key={product.img} title={product.title} price={product.price} img={product.img}/>
//               ))}
//         </div>

//       </div>
//       {/* What Clients Say */}
//       <div className='mt-10'>
//         <div className="flex flex-col items-center justify-center">
//           <h2 className="text-neutral-1000 mb-ml text-3xl font-bold md:text-4xl dark:text-neutral-100">
//           What Clients Say
//           </h2>
//         </div>
//         <div className=' flex flex-wrap gap-5 justify-center  py-8'>
//          {clientSays?.map((say)=>(
//                   <ClientSays key={say.say} name={say.name} job={say.job} say={say.say}/>
//               ))}
//         </div>

//       </div>
//       {/* Ready to Transform Your Life? */}
//       <div className='my-15'>
//        <StartNowSection />

//       </div>
//       <div className='mt-30'>
//        <Footer />

//       </div>
//     </div>
//   );
// }
'use client';

import { motion, useInView, useAnimation } from 'framer-motion';
import { useRef, useEffect, useState, useMemo } from 'react';
import type { Variants } from 'framer-motion';
import ProductCard from '@/components/blocks/bestProducts/ProductCard';
import GeneralBTN from '@/components/blocks/button/GeneralBTN';
import ClientSays from '@/components/blocks/clientSyas/ClientSays';
import IconCards from '@/components/blocks/IconCards/IconCards';
import SectionHeading from '@/components/molecules/SectionHeading';
import ComingEvent from '@/components/sections/ComingEvent/ComingEvent';
import StartNowSection from '@/components/sections/StartNowSection/StartNowSection';
import { JSX } from 'react/jsx-runtime';

// Types
interface CorePhilosophyCard {
  icon: string;
  title: string;
  description: string;
}

interface Product {
  title: string;
  price: string;
  img: string;
}

interface ClientTestimonial {
  say: string;
  name: string;
  job: string;
}

// Enhanced Animation Variants
const animationVariants: Record<string, Variants> = {
  fadeInUp: {
    hidden: {
      opacity: 0,
      y: 80,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94],
        type: 'spring',
        stiffness: 100,
      },
    },
  },

  fadeInLeft: {
    hidden: {
      opacity: 0,
      x: -100,
      scale: 0.9,
    },
    visible: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: {
        duration: 0.9,
        ease: [0.25, 0.46, 0.45, 0.94],
        type: 'spring',
        stiffness: 80,
      },
    },
  },

  fadeInRight: {
    hidden: {
      opacity: 0,
      x: 100,
      scale: 0.9,
    },
    visible: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: {
        duration: 0.9,
        ease: [0.25, 0.46, 0.45, 0.94],
        type: 'spring',
        stiffness: 80,
      },
    },
  },

  staggerContainer: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2,
        when: 'beforeChildren',
      },
    },
  },

  scaleIn: {
    hidden: {
      opacity: 0,
      scale: 0.6,
      rotateY: -15,
    },
    visible: {
      opacity: 1,
      scale: 1,
      rotateY: 0,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94],
        type: 'spring',
        stiffness: 120,
      },
    },
  },

  slideInFromBottom: {
    hidden: {
      opacity: 0,
      y: 120,
      scale: 0.8,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 1.2,
        ease: [0.25, 0.46, 0.45, 0.94],
        type: 'spring',
        stiffness: 60,
      },
    },
  },

  heroText: {
    hidden: {
      opacity: 0,
      y: 50,
      scale: 0.9,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 1.2,
        ease: [0.25, 0.46, 0.45, 0.94],
        delay: 0.5,
      },
    },
  },
};

// Custom hook for enhanced scroll animations
const useEnhancedScrollAnimation = (threshold: number = 0.1) => {
  const ref = useRef<HTMLDivElement>(null);
  const controls = useAnimation();
  const isInView = useInView(ref, {
    once: true,
    margin: '-50px',
    amount: threshold,
  });

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);

  return { ref, controls, isInView };
};

// Main Component
const HomePage = (): JSX.Element => {
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Enhanced scroll animations
  const { ref: heroRef, controls: heroControls } = useEnhancedScrollAnimation();
  const { ref: aboutRef, controls: aboutControls } =
    useEnhancedScrollAnimation(0.2);
  const { ref: servicesRef, controls: servicesControls } =
    useEnhancedScrollAnimation(0.3);
  const { ref: eventRef, controls: eventControls } =
    useEnhancedScrollAnimation(0.2);
  const { ref: productsRef, controls: productsControls } =
    useEnhancedScrollAnimation(0.2);
  const { ref: testimonialsRef, controls: testimonialsControls } =
    useEnhancedScrollAnimation(0.2);
  const { ref: ctaRef, controls: ctaControls } =
    useEnhancedScrollAnimation(0.3);

  // Mouse tracking for parallax effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    setIsLoaded(true);

    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Memoized data to optimize performance
  const corePhilosophyCards: CorePhilosophyCard[] = useMemo(
    () => [
      {
        icon: '/assets/svgs/heart.svg',
        title: 'Clarity blooms from struggle',
        description:
          'Your greatest challenges contain the seeds of your greatest breakthroughs. I help you transform pain into purpose and obstacles into opportunities.',
      },
      {
        icon: '/assets/svgs/goals.svg',
        title: 'Action creates momentum',
        description:
          "Transformation requires courage to face the truth about where you are and where you want to go. We'll have the conversations that matter most.",
      },
      {
        icon: '/assets/svgs/mind.svg',
        title: 'Growth requires courage',
        description:
          "Purpose isn't something you discover lying around—it's something you create through aligned action, clear values, and consistent commitment to growth.",
      },
    ],
    []
  );

  const bestProducts: Product[] = useMemo(
    () => [
      {
        title: 'eBook: The Clarity Code',
        price: '$29.99',
        img: '/assets/landing-page/Frame 99-2.webp',
      },
      {
        title: 'eBook: The Clarity Code',
        price: '$29.99',
        img: '/assets/landing-page/Frame 99-1.webp',
      },
      {
        title: 'eBook: The Clarity Code',
        price: '$29.99',
        img: '/assets/landing-page/Frame 99.webp',
      },
    ],
    []
  );

  const clientTestimonials: ClientTestimonial[] = useMemo(
    () => [
      {
        say: "Working with Coach Morgan completely transformed my approach to leadership. I've never felt more confident in my abilities",
        name: 'Sarah Johnson',
        job: 'CEO, TechStart Inc.',
      },
      {
        say: "Working with Coach Morgan completely transformed my approach to leadership. I've never felt more confident in my abilities.",
        name: 'Michael Chen',
        job: 'Entrepreneur',
      },
      {
        say: "Working with Coach Morgan completely transformed my approach to leadership. I've never felt more confident in my abilities.",
        name: 'Lisa Rodriguez',
        job: 'VP of Marketing',
      },
    ],
    []
  );

  return (
    <div className="overflow-hidden">
      {/* Hero Section */}
      <motion.section
        ref={heroRef}
        className="relative h-screen w-full overflow-hidden"
        initial="hidden"
        animate={heroControls}
        variants={animationVariants.fadeInUp}
      >
        {/* Video Background with enhanced loading */}
        <motion.video
          autoPlay
          muted
          loop
          playsInline
          preload="metadata"
          className="absolute top-0 left-0 z-0 h-full w-full object-cover"
          poster="/fallback.jpg"
          initial={{ scale: 1.1, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 3, ease: 'easeOut' }}
          style={{
            transform: `translate(${mousePosition.x * 0.01}px, ${mousePosition.y * 0.01}px)`,
          }}
        >
          <source
            src="/Videos/hero-section-high.mp4"
            type="video/mp4"
            media="(min-width: 1024px)"
          />
          <source
            src="/Videos/hero-section-low.mp4"
            type="video/mp4"
            media="(min-width: 768px)"
          />
          <source src="/hero-mobile.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </motion.video>

        {/* Enhanced Overlay with gradient */}
        <motion.div
          className="absolute z-10 flex h-full w-full items-center justify-center bg-gradient-to-b from-black/40 via-black/60 to-black/80 text-white"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 2 }}
        />

        {/* Hero Content with enhanced animations */}
        <motion.div className="relative z-20 flex h-full items-center justify-center bg-transparent text-white">
          <motion.div
            initial="hidden"
            animate={isLoaded ? 'visible' : 'hidden'}
            variants={animationVariants.staggerContainer}
            className="px-4 text-center"
          >
            <motion.div variants={animationVariants.heroText}>
              <motion.h1
                className="mb-6 bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-4xl leading-tight font-bold text-transparent md:text-6xl lg:text-7xl"
                animate={{
                  y: [-5, 5, -5],
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  ease: 'easeInOut',
                }}
              >
                You&lsquo;re One Decision Away <br />
                from a{' '}
                <motion.span
                  className="text-red-400"
                  animate={{
                    color: ['#f87171', '#ef4444', '#dc2626', '#f87171'],
                    textShadow: [
                      '0 0 10px rgba(239, 68, 68, 0.5)',
                      '0 0 20px rgba(239, 68, 68, 0.8)',
                      '0 0 10px rgba(239, 68, 68, 0.5)',
                    ],
                  }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  Better Life
                </motion.span>
              </motion.h1>

              <motion.p
                className="mx-auto max-w-3xl text-lg leading-relaxed text-gray-200 md:text-xl"
                variants={animationVariants.heroText}
              >
                Whether you&lsquo;re feeling stuck or ready for more, this is
                your moment. Let&lsquo;s build the mindset, the plan, and the
                momentum together.
              </motion.p>
            </motion.div>

            <motion.div
              className="mt-8 flex flex-col items-center justify-center gap-4 sm:flex-row"
              variants={animationVariants.heroText}
            >
              <div>
                <GeneralBTN text="Book a Session" size="400px" height="45px" />
              </div>
              <div>
                <GeneralBTN
                  text="Log In"
                  size="400px"
                  textColor=""
                  bg="transparent"
                  margin="m-2"
                  border="1"
                  height="45px"
                  className="my-3"
                />
              </div>
            </motion.div>
          </motion.div>
        </motion.div>
      </motion.section>

      {/* About Section */}
      <motion.section
        ref={aboutRef}
        className="px-4 py-20"
        initial="hidden"
        animate={aboutControls}
        variants={animationVariants.fadeInUp}
      >
        <motion.div
          className="mx-auto max-w-6xl text-center"
          variants={animationVariants.staggerContainer}
        >
          <motion.div variants={animationVariants.scaleIn}>
            <SectionHeading
              headline="Hi, I'm Mahmoud"
              subLine="With over 15 years of experience in personal development and business coaching, I'm passionate about helping ambitious individuals break through their limitations and create extraordinary results in their lives and careers."
            />
          </motion.div>
          <motion.div variants={animationVariants.scaleIn} className="mt-8">
            <GeneralBTN
              text="Explore Programs"
              border="1"
              size="327px"
              height="50px"
              bg="#F8F9FA"
              textColor="black"
            />
          </motion.div>
        </motion.div>
      </motion.section>

      {/* Services Section */}
      <motion.section
        ref={servicesRef}
        className="px-4 py-20"
        initial="hidden"
        animate={servicesControls}
        variants={animationVariants.fadeInUp}
      >
        <motion.div
          className="mx-auto max-w-6xl"
          variants={animationVariants.staggerContainer}
        >
          <motion.h2
            className="text-neutral-1000 mb-16 text-center text-3xl font-bold md:text-5xl dark:text-neutral-100"
            variants={animationVariants.scaleIn}
          >
            What I Offer
          </motion.h2>
          <motion.div variants={animationVariants.staggerContainer}>
            <IconCards cards={corePhilosophyCards} columns={3} />
          </motion.div>
        </motion.div>
      </motion.section>

      {/* Coming Event Section */}
      <motion.section
        ref={eventRef}
        className="relative overflow-hidden py-20"
        initial="hidden"
        animate={eventControls}
        variants={animationVariants.slideInFromBottom}
      >
        <motion.div
          className="absolute top-1/2 left-[-10%] z-0 h-96 w-96 -translate-y-1/2 rounded-full bg-gradient-to-r from-[#f6caca] via-white to-[#e8b1b1] opacity-30 blur-3xl dark:bg-[#F14646] dark:opacity-10"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
            x: [0, 50, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: 'linear',
          }}
        />
        <motion.div
          className="absolute top-1/2 right-[-10%] z-0 h-96 w-96 -translate-y-1/2 rounded-full bg-gradient-to-r from-[#dd8d8d] via-white to-[#e8b1b1] opacity-30 blur-3xl dark:bg-[#F14646] dark:opacity-10"
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
            x: [0, -50, 0],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: 'linear',
          }}
        />

        <motion.div
          className="relative z-10 mx-auto max-w-6xl px-4"
          variants={animationVariants.scaleIn}
        >
          <ComingEvent />
        </motion.div>
      </motion.section>

      {/* Best-Selling Products */}
      <motion.section
        ref={productsRef}
        className="relative overflow-hidden px-4 py-20"
        initial="hidden"
        animate={productsControls}
        variants={animationVariants.fadeInUp}
      >
        {/* Background decoration */}
        <motion.div
          className="absolute inset-0 z-0"
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.03 }}
          transition={{ duration: 2 }}
        >
          <div className="absolute top-1/4 left-1/4 h-72 w-72 animate-pulse rounded-full bg-blue-400 mix-blend-multiply blur-xl filter"></div>
          <div className="absolute top-3/4 right-1/4 h-72 w-72 animate-pulse rounded-full bg-purple-400 mix-blend-multiply blur-xl filter delay-1000"></div>
        </motion.div>

        <motion.div className="relative z-10 mx-auto max-w-6xl">
          <motion.h2
            className="text-neutral-1000 mb-16 text-center text-3xl font-bold md:text-5xl dark:text-neutral-100"
            variants={animationVariants.scaleIn}
            // animate={{
            //   backgroundImage: [
            //     "linear-gradient(45deg, #000, #333, #000)",
            //     "linear-gradient(45deg, #333, #666, #333)",
            //     "linear-gradient(45deg, #000, #333, #000)"
            //   ]
            // }}
            transition={{ duration: 4, repeat: Infinity }}
          >
            Best-Selling Products
          </motion.h2>

          <motion.div
            className="flex flex-wrap justify-center gap-3"
            variants={animationVariants.staggerContainer}
            initial="hidden"
            animate={productsControls}
          >
            {bestProducts.map((product, index) => (
              <motion.div
                key={`${product.img}-${index}`}
                variants={animationVariants.scaleIn}
                initial="hidden"
                animate="visible"
                transition={{
                  delay: index * 0.2,
                  duration: 0.8,
                  type: 'spring',
                  stiffness: 100,
                }}
                className="w-auto"
              >
                <motion.div
                  className="rounded-2xl"
                  animate={{
                    boxShadow: [
                      '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                      '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                      '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    ],
                  }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  <ProductCard
                    title={product.title}
                    price={product.price}
                    img={product.img}
                  />
                </motion.div>

                {/* Floating badges */}
                <motion.div
                  className="absolute -top-2 -right-2 z-20 rounded-full bg-red-500 px-2 py-1 text-xs font-bold text-white"
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{
                    scale: 1,
                    rotate: 0,
                    transition: {
                      delay: index * 0.2 + 0.5,
                      type: 'spring',
                      stiffness: 200,
                    },
                  }}
                >
                  HOT
                </motion.div>

                {/* Pulse effect */}
                <motion.div
                  className="absolute inset-0 rounded-2xl border-2 border-blue-400 opacity-0"
                  animate={{
                    scale: [1, 1.05, 1],
                    opacity: [0, 0.3, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: index * 0.3,
                    ease: 'easeInOut',
                  }}
                />
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </motion.section>

      {/* Client Testimonials */}
      <motion.section
        ref={testimonialsRef}
        className="bg-gray-50 px-4 py-20 dark:bg-gray-900"
        initial="hidden"
        animate={testimonialsControls}
        variants={animationVariants.fadeInUp}
      >
        <motion.div className="mx-auto max-w-6xl">
          <motion.h2
            className="text-neutral-1000 mb-16 text-center text-3xl font-bold md:text-5xl dark:text-neutral-100"
            variants={animationVariants.scaleIn}
          >
            What Clients Say
          </motion.h2>
          <motion.div
            className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3"
            variants={animationVariants.staggerContainer}
          >
            {clientTestimonials.map((testimonial, index) => (
              <motion.div
                key={`${testimonial.name}-${index}`}
                variants={
                  index % 2 === 0
                    ? animationVariants.fadeInLeft
                    : animationVariants.fadeInRight
                }
              >
                <ClientSays
                  name={testimonial.name}
                  job={testimonial.job}
                  say={testimonial.say}
                />
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </motion.section>

      {/* CTA Section */}
      <motion.section
        ref={ctaRef}
        className="py-20"
        initial="hidden"
        animate={ctaControls}
        variants={animationVariants.slideInFromBottom}
      >
        <motion.div variants={animationVariants.scaleIn} className="mx-auto">
          <StartNowSection />
        </motion.div>
      </motion.section>
    </div>
  );
};

export default HomePage;
