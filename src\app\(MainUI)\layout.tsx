import type { Metadata } from 'next';
import Navbar from '@/components/blocks/navbar/Navbar';
import Footer from '@/components/sections/footer/Footer';

export const metadata: Metadata = {
  title: 'Personal Coach',
  description: 'Generated by create next app',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      <nav className="nav-menu fixed z-30 w-full">
        <Navbar />
      </nav>

      <div className="relative z-10 flex-1">{children}</div>

      <footer className="footer z-10">
        <Footer />
      </footer>
    </>
  );
}
