import { useEffect, useRef } from 'react';
import { useAppDispatch, useAppSelector } from './reduxHooks';
import { checkAuthStatus } from '@/store/slices/auth/authSlice';


export const useAuth = () => {
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, loading, error } = useAppSelector((state) => state.auth);
  const hasAttemptedAuth = useRef(false);

  useEffect(() => {
    if (user) {
      hasAttemptedAuth.current = false;
    }
  }, [user]);

  useEffect(() => {
    if (!user && !loading && !hasAttemptedAuth.current) {
      console.log('useAuth: Checking authentication status...');
      console.log('useAuth: Current cookies:', document.cookie);
      hasAttemptedAuth.current = true;
      dispatch(checkAuthStatus());
    }
  }, [dispatch, user, loading]);

  const refreshAuth = () => {
    console.log('useAuth: Manual authentication refresh triggered');
    hasAttemptedAuth.current = true;
    dispatch(checkAuthStatus());
  };

  return {
    user,
    isAuthenticated,
    loading,
    error,
    refreshAuth,
  };
};
export const useIsAuthenticated = () => {
  const { isAuthenticated, loading } = useAuth();
  
  return {
    isAuthenticated,
    isLoading: loading,
  };
};
