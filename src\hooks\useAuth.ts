import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from './reduxHooks';
import { checkAuthStatus } from '@/store/slices/auth/authSlice';

/**
 * Custom hook for authentication management
 * Automatically checks auth status on mount and handles token refresh
 *
 * Features:
 * - Automatic authentication check on mount
 * - Token refresh handling (if /auth/me returns 401, tries /auth/refresh-token)
 * - Prevents duplicate API calls
 * - Works with cookie-based authentication
 *
 * Usage:
 * const { user, isAuthenticated, loading, error, refreshAuth } = useAuth();
 */
export const useAuth = () => {
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, loading, error } = useAppSelector((state) => state.auth);

  // Check authentication status on hook initialization
  useEffect(() => {
    // Only check if we don't already have a user and we're not currently loading
    if (!user && !loading) {
      console.log('useAuth: Checking authentication status...');
      dispatch(checkAuthStatus());
    }
  }, [dispatch, user, loading]);

  // Function to manually refresh authentication status
  const refreshAuth = () => {
    console.log('useAuth: Manual authentication refresh triggered');
    dispatch(checkAuthStatus());
  };

  return {
    user,
    isAuthenticated,
    loading,
    error,
    refreshAuth,
  };
};

/**
 * Hook specifically for checking if user is authenticated
 * Returns boolean and handles loading state
 */
export const useIsAuthenticated = () => {
  const { isAuthenticated, loading } = useAuth();
  
  return {
    isAuthenticated,
    isLoading: loading,
  };
};
