import { useEffect, useRef } from 'react';
import { useAppDispatch, useAppSelector } from './reduxHooks';
import { checkAuthStatus } from '@/store/slices/auth/authSlice';


export const useAuth = () => {
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, loading, error } = useAppSelector((state) => state.auth);
  const hasAttemptedAuth = useRef(false);

  // Reset attempt flag when user becomes authenticated
  useEffect(() => {
    if (user) {
      hasAttemptedAuth.current = false;
    }
  }, [user]);

  // Check authentication status on hook initialization
  useEffect(() => {
    // Only check once when the hook first mounts and we don't have a user
    if (!user && !loading && !hasAttemptedAuth.current) {
      console.log('useAuth: Checking authentication status...');
      hasAttemptedAuth.current = true;
      dispatch(checkAuthStatus());
    }
  }, [dispatch, user, loading]);

  // Function to manually refresh authentication status
  const refreshAuth = () => {
    console.log('useAuth: Manual authentication refresh triggered');
    hasAttemptedAuth.current = true;
    dispatch(checkAuthStatus());
  };

  return {
    user,
    isAuthenticated,
    loading,
    error,
    refreshAuth,
  };
};

/**
 * Hook specifically for checking if user is authenticated
 * Returns boolean and handles loading state
 */
export const useIsAuthenticated = () => {
  const { isAuthenticated, loading } = useAuth();
  
  return {
    isAuthenticated,
    isLoading: loading,
  };
};
