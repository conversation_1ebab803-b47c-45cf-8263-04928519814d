import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from './reduxHooks';
import { checkAuthStatus } from '@/store/slices/auth/authSlice';

/**
 * Custom hook for authentication management
 * Automatically checks auth status on mount and handles token refresh
 */
export const useAuth = () => {
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, loading, error } = useAppSelector((state) => state.auth);

  // Check authentication status on hook initialization
  useEffect(() => {
    // Only check if we don't already have a user and we're not currently loading
    if (!user && !loading) {
      dispatch(checkAuthStatus());
    }
  }, [dispatch, user, loading]);

  // Function to manually refresh authentication status
  const refreshAuth = () => {
    dispatch(checkAuthStatus());
  };

  return {
    user,
    isAuthenticated,
    loading,
    error,
    refreshAuth,
  };
};

/**
 * Hook specifically for checking if user is authenticated
 * Returns boolean and handles loading state
 */
export const useIsAuthenticated = () => {
  const { isAuthenticated, loading } = useAuth();
  
  return {
    isAuthenticated,
    isLoading: loading,
  };
};
