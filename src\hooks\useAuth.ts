import { useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAppDispatch, useAppSelector } from './reduxHooks';
import { checkAuthStatus } from '@/store/slices/auth/authSlice';


export const useAuth = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, loading, error } = useAppSelector((state) => state.auth);
  const hasAttemptedAuth = useRef(false);
  const isLoggedOut = useRef(false);
  const [showSessionExpiredToast, setShowSessionExpiredToast] = useState(false);

  // Reset attempt flag when user becomes authenticated
  useEffect(() => {
    if (user) {
      hasAttemptedAuth.current = false;
      isLoggedOut.current = false; // Reset logout flag when user is authenticated
    }
  }, [user]);

  // Handle session expiration
  useEffect(() => {
    if (error && error.includes('Your session has expired')) {
      console.log('Session expired - showing toast and redirecting to login');
      setShowSessionExpiredToast(true);

      // Redirect after showing toast
      setTimeout(() => {
        router.push('/auth/sign-in?reason=session-expired');
      }, 3000);
    }
  }, [error, router]);

  // Check authentication status on hook initialization
  useEffect(() => {
    if (!user && !loading && !hasAttemptedAuth.current) {
      console.log('useAuth: Checking authentication status...');
      hasAttemptedAuth.current = true;
      dispatch(checkAuthStatus());
    }
  }, [dispatch, user, loading]);

  // Function to manually refresh authentication status
  const refreshAuth = () => {
    console.log('useAuth: Manual authentication refresh triggered');
    hasAttemptedAuth.current = true;
    dispatch(checkAuthStatus());
  };

  return {
    user,
    isAuthenticated,
    loading,
    error,
    refreshAuth,
    showSessionExpiredToast,
    setShowSessionExpiredToast,
  };
};
export const useIsAuthenticated = () => {
  const { isAuthenticated, loading } = useAuth();
  
  return {
    isAuthenticated,
    isLoading: loading,
  };
};
