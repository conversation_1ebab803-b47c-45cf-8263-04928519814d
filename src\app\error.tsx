'use client';

import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { AlertTriangle, RefreshCw, Home, ArrowLeft } from 'lucide-react';
import GeneralBTN from '@/components/blocks/button/GeneralBTN';
import Image from 'next/image';

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

const ErrorPage: React.FC<ErrorPageProps> = ({ error, reset }) => {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Global error:', error);
  }, [error]);

  const animationVariants = {
    container: {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          staggerChildren: 0.2,
          delayChildren: 0.1,
        },
      },
    },
    item: {
      hidden: { opacity: 0, y: 20 },
      visible: {
        opacity: 1,
        y: 0,
        transition: { duration: 0.6, ease: 'easeOut' },
      },
    },
    float: {
      y: [-10, 10, -10],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  };

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-neutral-100 via-white to-neutral-200 dark:from-neutral-900 dark:via-black dark:to-neutral-800">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_70%,rgba(241,70,70,0.1),transparent_50%)]" />
        <div className="absolute inset-0 bg-[linear-gradient(135deg,transparent_25%,rgba(241,70,70,0.05)_50%,transparent_75%)]" />
      </div>

      {/* Floating Elements */}
      <motion.div
        className="absolute top-20 left-10 h-8 w-8 rounded-full bg-red-300 opacity-20"
        animate={animationVariants.float}
      />
      <motion.div
        className="absolute bottom-32 right-16 h-12 w-12 rounded-full bg-primary-300 opacity-15"
        animate={{
          ...animationVariants.float,
          transition: { ...animationVariants.float.transition, delay: 1 },
        }}
      />

      {/* Main Content */}
      <div className="relative z-10 flex min-h-screen items-center justify-center px-4 py-16">
        <motion.div
          className="w-full max-w-2xl text-center"
          variants={animationVariants.container}
          initial="hidden"
          animate="visible"
        >
          {/* Logo */}
          <motion.div
            className="mb-8 flex justify-center"
            variants={animationVariants.item}
          >
            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-white shadow-xl dark:bg-neutral-800">
              <Image
                src="/assets/landing-page/blackLogo.svg"
                alt="Personal Coach Logo"
                width={32}
                height={32}
                className="dark:hidden"
              />
              <Image
                src="/assets/landing-page/logo.svg"
                alt="Personal Coach Logo"
                width={32}
                height={32}
                className="hidden dark:block"
              />
            </div>
          </motion.div>

          {/* Error Icon */}
          <motion.div
            className="mb-8 flex justify-center"
            variants={animationVariants.item}
          >
            <motion.div
              className="relative"
              animate={{
                scale: [1, 1.05, 1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
            >
              {/* Glow Effect */}
              <motion.div
                className="absolute inset-0 rounded-full bg-red-500 opacity-20 blur-xl"
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.2, 0.4, 0.2],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: 'easeInOut',
                }}
              />
              
              <div className="relative z-10 flex h-24 w-24 items-center justify-center rounded-full bg-gradient-to-br from-red-100 to-red-200 shadow-2xl dark:from-red-900 dark:to-red-800">
                <AlertTriangle className="h-12 w-12 text-red-600 dark:text-red-400" />
              </div>
            </motion.div>
          </motion.div>

          {/* Error Message */}
          <motion.div
            className="mb-8 space-y-4"
            variants={animationVariants.item}
          >
            <h1 className="text-4xl font-bold text-neutral-800 dark:text-neutral-200 md:text-5xl">
              Oops! Something went wrong
            </h1>
            
            <p className="mx-auto max-w-lg text-lg text-neutral-600 dark:text-neutral-400">
              We encountered an unexpected error. Don't worry, our team has been notified and we're working to fix it.
            </p>

            {/* Error Details (Development) */}
            {process.env.NODE_ENV === 'development' && (
              <motion.div
                className="mx-auto mt-6 max-w-lg rounded-lg bg-red-50 p-4 text-left dark:bg-red-900/20"
                variants={animationVariants.item}
              >
                <h3 className="mb-2 font-semibold text-red-800 dark:text-red-400">
                  Error Details:
                </h3>
                <p className="text-sm text-red-700 dark:text-red-300">
                  {error.message}
                </p>
                {error.digest && (
                  <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                    Error ID: {error.digest}
                  </p>
                )}
              </motion.div>
            )}
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            className="flex flex-col items-center justify-center gap-4 sm:flex-row"
            variants={animationVariants.item}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <GeneralBTN
                text="Try Again"
                size="200px"
                height="45px"
                bg="primary-300"
                textColor="white"
                onClick={reset}
                className="flex items-center justify-center gap-2"
              />
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <GeneralBTN
                text="Go Home"
                size="200px"
                height="45px"
                bg="transparent"
                textColor="primary-300"
                border="1"
                href="/"
                className="flex items-center justify-center gap-2 border-primary-300"
              />
            </motion.div>
          </motion.div>

          {/* Help Text */}
          <motion.div
            className="mt-8 text-center"
            variants={animationVariants.item}
          >
            <p className="text-sm text-neutral-500 dark:text-neutral-500">
              If the problem persists, please{' '}
              <a
                href="mailto:<EMAIL>"
                className="text-primary-300 hover:underline"
              >
                contact our support team
              </a>
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default ErrorPage;
