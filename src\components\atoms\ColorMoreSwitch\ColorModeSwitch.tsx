'use client';

import React, { useEffect, useState } from 'react';
import { useTheme } from 'next-themes';
// import Image from 'next/image';
import { Sun, Moon } from 'lucide-react';

const ColorModeSwitch = () => {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div className="">
      <div className="p-2">
        {theme === 'dark' ? (
          <button
            onClick={() => setTheme('light')}
            className="text-gradient-to-br transform rounded-full from-orange-400 to-yellow-500 p-3 shadow-lg transition-all duration-300 hover:from-orange-500 hover:shadow-xl focus:outline-none"
          >
            <Sun size={17} className="text-orange-400 drop-shadow-sm" />
          </button>
        ) : (
          <button
            onClick={() => setTheme('dark')}
            className="transform rounded-full bg-gradient-to-br from-slate-700 to-slate-900 p-3 shadow-lg transition-all duration-300 hover:from-slate-600 hover:to-slate-800 focus:outline-none"
          >
            <Moon size={17} className="text-white drop-shadow-sm" />
          </button>
        )}
      </div>
    </div>
  );
};

export default ColorModeSwitch;
