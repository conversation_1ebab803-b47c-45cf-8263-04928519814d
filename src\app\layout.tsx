import type { Metadata } from 'next';
import { Poppins } from 'next/font/google';
import './globals.css';
import ReduxProvider from '@/components/ReduxProvider/ReduxProvider';
import { ThemeProvider } from 'next-themes';

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-poppins',
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${poppins.variable} antialiased`}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <ReduxProvider>
            {/* Background wrapper – shown only in dark mode */}
            <div className="relative flex min-h-screen flex-col overflow-hidden">
              {/* 🌙 Background layers (only in dark mode) */}
              <div className="absolute inset-0 hidden dark:block">
                <div
                  className="absolute inset-0 opacity-95"
                  style={{
                    backgroundColor: 'black',
                  }}
                />
                <div
                  className="opacity absolute inset-0"
                  style={{
                    backgroundImage:
                      'linear-gradient(90deg, #070707 2px, transparent 1px), linear-gradient(180deg, #070707 1px, transparent 1px)',
                    backgroundSize: '80px 80px',
                  }}
                />
                <div
                  className="opacity absolute inset-0"
                  style={{
                    backgroundImage:
                      'linear-gradient(90deg, #070707 1px, transparent 1px), linear-gradient(180deg, #070707 1px, transparent 1px)',
                    backgroundSize: '160px 160px',
                  }}
                />
              </div>

              <div className="relative z-10 flex-1">{children}</div>
            </div>
          </ReduxProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
