import Link from 'next/link';
import React from 'react';
import { cn } from '@/utils/cn';

interface Props {
  text: string;
  size?: string;
  bg?: string;
  textColor?: string;
  margin?: string;
  border?: string;
  height?: string;
  href?: string;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
}

function GeneralBTN(props: Props) {
  const {
    text = '',
    size,
    bg = 'primary-300',
    textColor,
    margin = '',
    border = '0',
    height,
    href,
    onClick,
    className,
    disabled = false,
  } = props;

  // Base classes that can be overridden
  const baseClasses = cn(
    margin,
    `border-${border}`,
    `bg-${bg}`,
    `text-${textColor}`,
    'rounded-full  font-semibold',
    className
  );

  return href ? (
    <Link
      href={href}
      style={{ width: size, height: height, backgroundColor: bg }}
      className={cn(className, 'flex items-center justify-center', baseClasses)}
    >
      {text}
    </Link>
  ) : (
    <button
      disabled={disabled}
      onClick={onClick}
      style={{ width: size, height: height, backgroundColor: bg }}
      className={cn(className, 'cursor-pointer', baseClasses)}
    >
      {text}
    </button>
  );
}

export default GeneralBTN;
