import Image from 'next/image';
import React from 'react';

interface Props {
  title: string;
  price: string;
  img: string;
}

function ProductCard({ title, price, img }: Props) {
  return (
    <div className="hover:border-primary-300 dark:hover:border-primary-300 flex h-[350px] w-[350px] cursor-pointer flex-col items-center justify-center overflow-hidden rounded-md border-1 shadow-lg dark:border-gray-700">
      <div className="relative h-full w-full border-b">
        <Image src={img} fill alt="e-book" className="object-cover" />
      </div>

      <div className="flex w-full flex-col gap-2 rounded-b-md p-2">
        <h1 className="text-start text-lg font-bold text-black dark:text-amber-50">
          {title}
        </h1>
        <h1 className="text-primary-300 text-start text-lg font-bold">
          {price}
        </h1>

        <button className="bg-primary-300 mt-2 rounded px-4 py-1 text-sm font-semibold text-white">
          Buy now
        </button>
      </div>
    </div>
  );
}

export default ProductCard;
