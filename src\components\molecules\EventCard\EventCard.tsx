import React from 'react';
import Image from 'next/image';

interface Event {
  id: number;
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  price: string;
  image: string;
  featured: boolean;
  countdown?: {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  };
}

interface EventCardProps {
  event: Event;
}

const EventCard: React.FC<EventCardProps> = ({ event }) => {
  const dateObj = new Date(event.date);
  const day = dateObj.getDate();
  const month = dateObj
    .toLocaleDateString('en-US', { month: 'short' })
    .toUpperCase();

  return (
    <div className="mb-6 flex flex-col gap-4 p-0 sm:flex-row sm:gap-1 md:items-start">
      <div className="h-[200px] w-full flex-shrink-0 sm:h-[200px] sm:w-[200px] md:h-[248px] md:w-[248px]">
        <Image
          src={event.image}
          alt={event.title}
          width={248}
          height={248}
          className="h-full w-full rounded-2xl object-center md:object-cover"
        />
      </div>

      <div className="text-primary-300 order-first h-fit flex-shrink-0 rounded-xl px-2 sm:order-none sm:px-3">
        <div className="text-2xl leading-none font-bold sm:text-3xl md:text-[40px]">
          {day}
        </div>
        <div className="mt-1 text-xs leading-none font-semibold sm:text-sm md:text-[14px]">
          {month}
        </div>
      </div>

      <div className="flex min-w-0 flex-1 flex-col gap-3 sm:gap-4">
        <h3 className="text-neutral-1000 mb-2 text-xl leading-tight font-semibold sm:mb-3 sm:text-2xl sm:font-normal md:text-3xl lg:text-4xl dark:text-neutral-100">
          {event.title}
        </h3>

        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <div className="bg-primary-300 rounded-full p-1">
              <Image
                src="/assets/svgs/location.svg"
                alt="Location"
                width={20}
                height={20}
                className="sm:h-6 sm:w-6"
              />
            </div>{' '}
            <span className="text-sm text-neutral-600 sm:text-base dark:text-neutral-300">
              {event.location}
            </span>
          </div>

          <div className="flex items-center gap-2">
            <div className="bg-primary-300 rounded-full p-1">
              <Image
                src="/assets/svgs/clock.svg"
                alt="Clock"
                width={20}
                height={20}
                className="sm:h-6 sm:w-6"
              />
            </div>
            <span className="text-sm text-neutral-600 sm:text-base dark:text-neutral-300">
              {event.time}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventCard;
