// /lib/axios.ts
import axios from 'axios';

export const axiosPublic = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
});

export const axiosPrivate = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  withCredentials: true,
});

// Add request interceptor for debugging
axiosPrivate.interceptors.request.use(
  (config) => {
    console.log('axiosPrivate request:', {
      url: config.url,
      method: config.method,
      baseURL: config.baseURL,
      withCredentials: config.withCredentials,
      headers: config.headers,
    });
    return config;
  },
  (error) => {
    console.error('axiosPrivate request error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for debugging
axiosPrivate.interceptors.response.use(
  (response) => {
    console.log('axiosPrivate response success:', {
      status: response.status,
      url: response.config.url,
    });
    return response;
  },
  (error) => {
    console.error('axiosPrivate response error:', {
      status: error.response?.status,
      message: error.response?.data?.message,
      url: error.config?.url,
    });
    return Promise.reject(error);
  }
);
