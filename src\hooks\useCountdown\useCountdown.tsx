import { CountdownResult, getCountdown } from '@/utils/Countdown';
import { useState, useEffect } from 'react';

export function useCountdown(targetDate: Date | string) {
  const [countdown, setCountdown] = useState<CountdownResult>(
    getCountdown(targetDate)
  );

  useEffect(() => {
    const interval = setInterval(() => {
      const newCountdown = getCountdown(targetDate);
      setCountdown(newCountdown);

      // إيقاف العداد لما ينتهي الوقت
      if (newCountdown.isExpired) {
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [targetDate]);

  return countdown;
}
