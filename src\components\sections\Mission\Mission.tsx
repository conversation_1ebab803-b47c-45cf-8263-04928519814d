import Image from 'next/image';
import React from 'react';

interface MissionCardProps {
  title: string;
  description: string;
  titleColor?: string;
}

const MissionCard: React.FC<MissionCardProps> = ({
  title,
  description,
  titleColor = 'text-red-500',
}) => {
  return (
    <div className="hover:border-primary-400 flex flex-col items-center rounded-xl border border-neutral-200 p-6 text-center shadow-sm transition-all duration-50 hover:border hover:shadow-md dark:border-none">
      <h3 className={`mb-3 text-xl font-bold ${titleColor}`}>{title}</h3>
      <p className="text-md leading-relaxed text-neutral-600 dark:text-neutral-300">
        {description}
      </p>
    </div>
  );
};

const Mission: React.FC = () => {
  const missionCards = [
    {
      title: '100 Million Lives',
      description: 'Touched and transformed by 2030',
      titleColor: 'text-red-500',
    },
    {
      title: 'Influential Leaders',
      description: 'Creating a generation of conscious leaders',
      titleColor: 'text-red-500',
    },
    {
      title: 'Better World',
      description: 'Contributing to global transformation',
      titleColor: 'text-red-500',
    },
  ];

  return (
    <section className="mx-auto w-full max-w-6xl px-4 py-12">
      <div className="mb-12 text-center">
        <div className="mb-ml bg-primary-10 w-4-xl h-4-xl mx-auto flex items-center justify-center rounded-full">
          <Image
            src="assets/svgs/global.svg"
            alt="our mission"
            width={28}
            height={28}
            className="h-7 w-7"
            style={{
              filter:
                'invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%)',
            }}
          />{' '}
        </div>
        <h2 className="mb-2 text-3xl font-bold text-neutral-900 md:text-4xl dark:text-neutral-50">
          Our Mission
        </h2>
      </div>

      {/* Mission Cards Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        {missionCards.map((card, index) => (
          <MissionCard
            key={index}
            title={card.title}
            description={card.description}
            titleColor={card.titleColor}
          />
        ))}
      </div>
    </section>
  );
};

export default Mission;
