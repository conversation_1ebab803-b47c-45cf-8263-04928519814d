import React from 'react';
import EventCard from '../../molecules/EventCard/EventCard';

interface Event {
  id: number;
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  price: string;
  image: string;
  featured: boolean;
  countdown?: {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  };
}

const events: Event[] = [
  {
    id: 2,
    title:
      'Grant Cardone Foundation | Mentor Workshop With Circle of Brotherhood',
    description:
      'Exclusive mentorship workshop with Circle of Brotherhood. Learn from industry leaders and transform your approach to business and life.',
    date: 'July 28, 2025',
    time: '9:45 am - 12:30 pm',
    location: 'JW Headquarters (Classroom)',
    price: '$150',
    image: '/assets/events/event-img.png',
    featured: false,
  },
  {
    id: 3,
    title:
      'Grant Cardone Foundation | Mentor Workshop With Circle of Brotherhood',
    description:
      'Exclusive mentorship workshop with Circle of Brotherhood. Learn from industry leaders and transform your approach to business and life.',
    date: 'July 28, 2025',
    time: '9:45 am - 12:30 pm',
    location: 'JW Headquarters (Classroom)',
    price: '$150',
    image: '/assets/events/event-img.png',
    featured: false,
  },
  {
    id: 4,
    title: 'Business Breakthrough Intensive',
    description:
      'A deep-dive workshop focused on breaking through business plateaus and scaling to the next level.',
    date: 'August 10, 2025',
    time: '10:00 am - 4:00 pm',
    location: 'Miami, FL',
    price: '$300',
    image: '/assets/events/event-img.png',
    featured: false,
  },
];
const EventsList = () => {
  const groupedEvents = events.reduce(
    (acc, event) => {
      const eventDate = new Date(event.date);
      const monthYear = eventDate.toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric',
      });

      if (!acc[monthYear]) {
        acc[monthYear] = [];
      }
      acc[monthYear].push(event);
      return acc;
    },
    {} as Record<string, Event[]>
  );

  return (
    <section className="mx-8 md:mx-18">
      <div id="upcoming-events" className="mb-12 text-center">
        <h2 className="mb-4 text-3xl font-bold text-neutral-900 md:text-4xl dark:text-neutral-100">
          Upcoming Events
        </h2>
      </div>

      {Object.entries(groupedEvents).map(([monthYear, monthEvents]) => (
        <div key={monthYear} className="mb-16">
          {monthYear !== 'All Events' && (
            <h3 className="mb-8 text-2xl font-bold text-neutral-900 md:text-3xl lg:text-4xl dark:text-neutral-100">
              {monthYear}
            </h3>
          )}

          {/* Events List */}
          <div className="space-y-6">
            {monthEvents.map((event) => (
              <EventCard key={event.id} event={event} />
            ))}
          </div>
        </div>
      ))}
    </section>
  );
};

export default EventsList;
