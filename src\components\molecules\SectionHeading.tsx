import React from 'react';

interface SectionHeadingProps {
  headline?: string;
  subLine?: string;
  className?: string;
}
const SectionHeading: React.FC<SectionHeadingProps> = ({
  headline,
  subLine,
  className = '',
}) => {
  return (
    <div className={` ${className}`}>
      {(headline || subLine) && (
        <div className="mb-4-xl text-center">
          {headline && (
            <h2 className="text-neutral-1000 mb-ml text-3xl font-bold md:text-4xl dark:text-neutral-100">
              {headline}
            </h2>
          )}
          {subLine && (
            <p className="mx-auto max-w-3xl text-lg leading-relaxed text-neutral-600 dark:text-neutral-300">
              {subLine}
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default SectionHeading;
