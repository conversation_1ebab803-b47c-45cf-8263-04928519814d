import React from 'react';
import type { Metada<PERSON> } from 'next';

export const metadata: Metadata = {
  title: 'Admimin Dashboard',
  description: 'Generated by create next app',
};

const AdminLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="flex">
      <aside className="flex-1/5">
        {/* Sidebar with dashboard links */}
        Iam A Side
      </aside>

      <main className="flex-4/5">
        {children} {/* Main dashboard content */}
      </main>
    </div>
  );
};

export default AdminLayout;
