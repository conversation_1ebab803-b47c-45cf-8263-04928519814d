'use client';

import React, { useEffect, useRef, useCallback, useState } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import Image from 'next/image';
import GeneralBTN from '../button/GeneralBTN';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'success' | 'error';
  header: string;
  description: string;
  primaryButtonText?: string;
  primaryButtonLink?: string;
  onPrimaryButtonClick?: () => void;
  secondaryButtonText?: string;
  secondaryButtonLink?: string;
  onSecondaryButtonClick?: () => void;
  timer?: number;
  position?: 'middle' | 'top';
}

const modalAnimationVariants: Record<string, Variants> = {
  overlay: {
    hidden: {
      opacity: 0,
      backdropFilter: 'blur(0px)',
    },
    visible: {
      opacity: 1,
      backdropFilter: 'blur(8px)',
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
    exit: {
      opacity: 0,
      backdropFilter: 'blur(0px)',
      transition: {
        duration: 0.2,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
  },
  modal: {
    hidden: {
      opacity: 0,
      scale: 0.8,
      y: 50,
      rotateX: -15,
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      rotateX: 0,
      transition: {
        duration: 0.5,
        ease: [0.25, 0.46, 0.45, 0.94],
        type: 'spring',
        stiffness: 300,
        damping: 30,
      },
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      y: -20,
      transition: {
        duration: 0.2,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
  },
  content: {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.1,
        duration: 0.4,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
  },
  icon: {
    hidden: {
      scale: 0,
      rotate: -180,
      opacity: 0,
    },
    visible: {
      scale: 1,
      rotate: 0,
      opacity: 1,
      transition: {
        delay: 0.2,
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94],
        type: 'spring',
        stiffness: 200,
      },
    },
  },
  buttons: {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.3,
        duration: 0.4,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
  },
};

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  type,
  header,
  description,
  primaryButtonText,
  primaryButtonLink,
  onPrimaryButtonClick,
  secondaryButtonText,
  secondaryButtonLink,
  onSecondaryButtonClick,
  timer,
  position = 'middle',
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const previousActiveElement = useRef<HTMLElement | null>(null);
  const firstFocusableElement = useRef<HTMLElement | null>(null);
  const lastFocusableElement = useRef<HTMLElement | null>(null);
  const [timeLeft, setTimeLeft] = useState<number>(timer || 0);

  const setFocusableElements = useCallback(() => {
    if (!modalRef.current) return;

    const focusableElements = modalRef.current.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    firstFocusableElement.current = focusableElements[0] as HTMLElement;
    lastFocusableElement.current = focusableElements[
      focusableElements.length - 1
    ] as HTMLElement;
  }, []);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (!isOpen) return;

      if (e.key === 'Escape') {
        onClose();
        return;
      }

      if (e.key === 'Tab') {
        if (!firstFocusableElement.current || !lastFocusableElement.current)
          return;

        if (e.shiftKey) {
          if (document.activeElement === firstFocusableElement.current) {
            e.preventDefault();
            lastFocusableElement.current.focus();
          }
        } else {
          if (document.activeElement === lastFocusableElement.current) {
            e.preventDefault();
            firstFocusableElement.current.focus();
          }
        }
      }
    },
    [isOpen, onClose]
  );

  const handleAnyClick = useCallback(() => {
    onClose();
  }, [onClose]);

  const handlePrimaryButtonClick = useCallback(() => {
    if (onPrimaryButtonClick) {
      onPrimaryButtonClick();
    }
    if (!primaryButtonLink) {
      onClose();
    }
  }, [onPrimaryButtonClick, onClose, primaryButtonLink]);

  const handleSecondaryButtonClick = useCallback(() => {
    if (onSecondaryButtonClick) {
      onSecondaryButtonClick();
    }
    if (!secondaryButtonLink) {
      onClose();
    }
  }, [onSecondaryButtonClick, onClose, secondaryButtonLink]);

  useEffect(() => {
    if (isOpen && timer && timer > 0) {
      setTimeLeft(timer);

      const interval = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 100) {
            onClose();
            return 0;
          }
          return prev - 100;
        });
      }, 100);

      return () => clearInterval(interval);
    }
  }, [isOpen, timer, onClose]);

  useEffect(() => {
    if (isOpen) {
      previousActiveElement.current = document.activeElement as HTMLElement;
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';

      setTimeout(() => {
        setFocusableElements();
        if (firstFocusableElement.current) {
          firstFocusableElement.current.focus();
        }
      }, 150);
    } else {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';

      if (previousActiveElement.current) {
        previousActiveElement.current.focus();
      }
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, handleKeyDown, setFocusableElements]);

  if (!isOpen) return null;

  const iconSrc =
    type === 'success'
      ? '/assets/svgs/success.svg'
      : '/assets/svgs/warning.svg';
  const iconBgColor = type === 'success' ? 'bg-[#34C75933]' : 'bg-primary-20';

  const getPositionClasses = () => {
    switch (position) {
      case 'top':
        return 'items-start pt-20';
      case 'middle':
      default:
        return 'items-center';
    }
  };

  const modalContent = (
    <AnimatePresence mode="wait">
      {isOpen && (
        <motion.div
          className={`fixed inset-0 z-50 flex ${getPositionClasses()} justify-center p-4`}
          onClick={handleAnyClick}
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-title"
          aria-describedby="modal-description"
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={modalAnimationVariants.overlay}
        >
          <motion.div
            className="absolute inset-0 bg-black/20"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          />

          <motion.div
            ref={modalRef}
            className="relative mx-auto w-full max-w-lg"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={modalAnimationVariants.modal}
          >
            <div className="relative overflow-hidden rounded-2xl border border-neutral-700 bg-[#0D0D0D] shadow-2xl shadow-black/50">
              <motion.div
                className="p-2 text-center sm:p-4 sm:px-8"
                variants={modalAnimationVariants.content}
              >
                <motion.div
                  className="mb-1 flex justify-center"
                  variants={modalAnimationVariants.icon}
                >
                  <div className="relative flex h-24 w-24 items-center justify-center">
                    {timer && timer > 0 && (
                      <svg
                        className="absolute inset-0 h-full w-full"
                        viewBox="0 0 96 96"
                        style={{ transform: 'rotate(-90deg)' }}
                      >
                        <circle
                          cx="48"
                          cy="48"
                          r="46"
                          fill="none"
                          stroke="rgba(255, 255, 255, 0.1)"
                          strokeWidth="2"
                        />
                        <motion.circle
                          cx="48"
                          cy="48"
                          r="46"
                          fill="none"
                          stroke={type === 'success' ? '#10b981' : '#ef4444'}
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeDasharray={`${2 * Math.PI * 46}`}
                          initial={{ strokeDashoffset: 0 }}
                          animate={{
                            strokeDashoffset:
                              2 * Math.PI * 46 * (1 - timeLeft / (timer || 1)),
                          }}
                          transition={{ duration: 0.1, ease: 'linear' }}
                        />
                      </svg>
                    )}

                    <div
                      className={`h-20 w-20 rounded-full ${iconBgColor} relative z-10 flex items-center justify-center shadow-lg`}
                    >
                      <Image
                        src={iconSrc}
                        alt={type === 'success' ? 'Success' : 'Error'}
                        width={48}
                        height={48}
                        className="h-12 w-12"
                      />
                    </div>
                  </div>
                </motion.div>

                <h2
                  id="modal-title"
                  className="mb-4 text-lg leading-tight font-bold text-white"
                >
                  {header}
                </h2>

                <div className="">
                  <p
                    id="modal-description"
                    className="text-base leading-relaxed text-neutral-300"
                  >
                    {description}
                  </p>
                </div>

                {(primaryButtonText || secondaryButtonText) && (
                  <motion.div
                    className={`mt-2 flex w-full flex-col gap-3 sm:flex-row md:gap-2 ${
                      primaryButtonText && secondaryButtonText
                        ? ''
                        : 'sm:flex-col'
                    }`}
                    variants={modalAnimationVariants.buttons}
                  >
                    {secondaryButtonText && (
                      <div className="flex-1">
                        <GeneralBTN
                          text={secondaryButtonText}
                          href={secondaryButtonLink}
                          onClick={
                            secondaryButtonLink
                              ? undefined
                              : handleSecondaryButtonClick
                          }
                          height="38px"
                          bg="transparent"
                          textColor="primary-500"
                          border="border border-neutral-600"
                          className="border-primary-400 text-primary-400 w-full rounded-full border text-base font-semibold"
                        />
                      </div>
                    )}

                    {primaryButtonText && (
                      <div className="flex-1">
                        <GeneralBTN
                          text={primaryButtonText}
                          href={primaryButtonLink}
                          onClick={
                            primaryButtonLink
                              ? undefined
                              : handlePrimaryButtonClick
                          }
                          height="38px"
                          bg="red-500"
                          textColor="white"
                          className="w-full rounded-full text-base font-semibold"
                        />
                      </div>
                    )}
                  </motion.div>
                )}
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  return typeof window !== 'undefined'
    ? createPortal(modalContent, document.body)
    : null;
};

export default Modal;
